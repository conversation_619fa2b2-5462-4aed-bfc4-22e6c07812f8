[2025-09-12 16:41:28] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.00/training/checkpoints/checkpoint_iter_000105.pkl
[2025-09-12 16:41:52] ✓ 从checkpoint加载参数: 105
[2025-09-12 16:41:52]   - 能量: -51.315226+0.000807j ± 0.087495
[2025-09-12 16:41:52] ================================================================================
[2025-09-12 16:41:52] 加载量子态: L=4, J2=0.03, J1=0.00, checkpoint=checkpoint_iter_000105
[2025-09-12 16:41:52] 使用采样数目: 1048576
[2025-09-12 16:41:52] 设置样本数为: 1048576
[2025-09-12 16:41:52] 开始生成共享样本集...
[2025-09-12 16:43:50] 样本生成完成,耗时: 117.799 秒
[2025-09-12 16:43:50] ================================================================================
[2025-09-12 16:43:50] 开始计算自旋结构因子...
[2025-09-12 16:43:50] 初始化操作符缓存...
[2025-09-12 16:43:50] 预构建所有自旋相关操作符...
[2025-09-12 16:43:50] 开始计算自旋相关函数...
[2025-09-12 16:44:03] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 12.680s
[2025-09-12 16:44:20] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.787s
[2025-09-12 16:44:30] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.369s
[2025-09-12 16:44:39] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.311s
[2025-09-12 16:44:48] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.350s
[2025-09-12 16:44:58] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.371s
[2025-09-12 16:45:07] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.357s
[2025-09-12 16:45:17] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.364s
[2025-09-12 16:45:26] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.325s
[2025-09-12 16:45:35] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.392s
[2025-09-12 16:45:45] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.360s
[2025-09-12 16:45:54] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.370s
[2025-09-12 16:46:03] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.355s
[2025-09-12 16:46:13] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.369s
[2025-09-12 16:46:22] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.355s
[2025-09-12 16:46:32] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.343s
[2025-09-12 16:46:41] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.369s
[2025-09-12 16:46:50] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.359s
[2025-09-12 16:47:00] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.353s
[2025-09-12 16:47:09] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.383s
[2025-09-12 16:47:18] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.412s
[2025-09-12 16:47:28] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.381s
[2025-09-12 16:47:37] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.354s
[2025-09-12 16:47:47] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.367s
[2025-09-12 16:47:56] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.357s
[2025-09-12 16:48:05] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.359s
[2025-09-12 16:48:15] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.357s
[2025-09-12 16:48:24] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.364s
[2025-09-12 16:48:33] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.353s
[2025-09-12 16:48:43] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.358s
[2025-09-12 16:48:52] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.394s
[2025-09-12 16:49:02] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.420s
[2025-09-12 16:49:11] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.367s
[2025-09-12 16:49:20] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.377s
[2025-09-12 16:49:30] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.350s
[2025-09-12 16:49:39] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.359s
[2025-09-12 16:49:49] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.365s
[2025-09-12 16:49:58] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.355s
[2025-09-12 16:50:07] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.356s
[2025-09-12 16:50:17] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.356s
[2025-09-12 16:50:26] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.354s
[2025-09-12 16:50:35] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.372s
[2025-09-12 16:50:45] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.301s
[2025-09-12 16:50:54] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.366s
[2025-09-12 16:51:03] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.355s
[2025-09-12 16:51:13] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.353s
[2025-09-12 16:51:22] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.359s
[2025-09-12 16:51:32] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.345s
[2025-09-12 16:51:41] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.353s
[2025-09-12 16:51:50] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.364s
[2025-09-12 16:52:00] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.350s
[2025-09-12 16:52:09] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.331s
[2025-09-12 16:52:18] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.404s
[2025-09-12 16:52:28] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.320s
[2025-09-12 16:52:37] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.345s
[2025-09-12 16:52:46] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.358s
[2025-09-12 16:52:56] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.355s
[2025-09-12 16:53:05] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.343s
[2025-09-12 16:53:15] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.374s
[2025-09-12 16:53:24] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.375s
[2025-09-12 16:53:33] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.356s
[2025-09-12 16:53:43] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.369s
[2025-09-12 16:53:52] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.375s
[2025-09-12 16:54:01] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.357s
[2025-09-12 16:54:02] 自旋相关函数计算完成,总耗时 611.61 秒
[2025-09-12 16:54:03] 计算傅里叶变换...
[2025-09-12 16:54:04] 自旋结构因子计算完成
[2025-09-12 16:54:06] 自旋相关函数平均误差: 0.000695
