[2025-09-12 18:44:31] 使用checkpoint文件: results/L=4/J2=0.03/J1=0.00/training/checkpoints/checkpoint_iter_001050.pkl
[2025-09-12 18:44:48] ✓ 从checkpoint加载参数: 1050
[2025-09-12 18:44:48]   - 能量: -51.382395+0.002728j ± 0.088047
[2025-09-12 18:44:48] ================================================================================
[2025-09-12 18:44:48] 加载量子态: L=4, J2=0.03, J1=0.00, checkpoint=checkpoint_iter_001050
[2025-09-12 18:44:48] 使用采样数目: 1048576
[2025-09-12 18:44:48] 设置样本数为: 1048576
[2025-09-12 18:44:48] 开始生成共享样本集...
[2025-09-12 18:47:52] 样本生成完成,耗时: 183.334 秒
[2025-09-12 18:47:52] ================================================================================
[2025-09-12 18:47:52] 开始计算自旋结构因子...
[2025-09-12 18:47:52] 初始化操作符缓存...
[2025-09-12 18:47:52] 预构建所有自旋相关操作符...
[2025-09-12 18:47:52] 开始计算自旋相关函数...
[2025-09-12 18:48:06] 自旋算符进度: 1/64, 当前算符: S_0 · S_0, 耗时: 14.570s
[2025-09-12 18:48:24] 自旋算符进度: 2/64, 当前算符: S_0 · S_1, 耗时: 17.755s
[2025-09-12 18:48:34] 自旋算符进度: 3/64, 当前算符: S_0 · S_2, 耗时: 9.336s
[2025-09-12 18:48:43] 自旋算符进度: 4/64, 当前算符: S_0 · S_3, 耗时: 9.319s
[2025-09-12 18:48:52] 自旋算符进度: 5/64, 当前算符: S_0 · S_4, 耗时: 9.293s
[2025-09-12 18:49:02] 自旋算符进度: 6/64, 当前算符: S_0 · S_5, 耗时: 9.330s
[2025-09-12 18:49:11] 自旋算符进度: 7/64, 当前算符: S_0 · S_6, 耗时: 9.338s
[2025-09-12 18:49:20] 自旋算符进度: 8/64, 当前算符: S_0 · S_7, 耗时: 9.322s
[2025-09-12 18:49:30] 自旋算符进度: 9/64, 当前算符: S_0 · S_8, 耗时: 9.314s
[2025-09-12 18:49:39] 自旋算符进度: 10/64, 当前算符: S_0 · S_9, 耗时: 9.397s
[2025-09-12 18:49:48] 自旋算符进度: 11/64, 当前算符: S_0 · S_10, 耗时: 9.249s
[2025-09-12 18:49:58] 自旋算符进度: 12/64, 当前算符: S_0 · S_11, 耗时: 9.322s
[2025-09-12 18:50:07] 自旋算符进度: 13/64, 当前算符: S_0 · S_12, 耗时: 9.338s
[2025-09-12 18:50:16] 自旋算符进度: 14/64, 当前算符: S_0 · S_13, 耗时: 9.325s
[2025-09-12 18:50:25] 自旋算符进度: 15/64, 当前算符: S_0 · S_14, 耗时: 9.304s
[2025-09-12 18:50:35] 自旋算符进度: 16/64, 当前算符: S_0 · S_15, 耗时: 9.338s
[2025-09-12 18:50:44] 自旋算符进度: 17/64, 当前算符: S_0 · S_16, 耗时: 9.319s
[2025-09-12 18:50:53] 自旋算符进度: 18/64, 当前算符: S_0 · S_17, 耗时: 9.319s
[2025-09-12 18:51:03] 自旋算符进度: 19/64, 当前算符: S_0 · S_18, 耗时: 9.322s
[2025-09-12 18:51:12] 自旋算符进度: 20/64, 当前算符: S_0 · S_19, 耗时: 9.323s
[2025-09-12 18:51:21] 自旋算符进度: 21/64, 当前算符: S_0 · S_20, 耗时: 9.315s
[2025-09-12 18:51:31] 自旋算符进度: 22/64, 当前算符: S_0 · S_21, 耗时: 9.333s
[2025-09-12 18:51:40] 自旋算符进度: 23/64, 当前算符: S_0 · S_22, 耗时: 9.344s
[2025-09-12 18:51:50] 自旋算符进度: 24/64, 当前算符: S_0 · S_23, 耗时: 9.326s
[2025-09-12 18:51:59] 自旋算符进度: 25/64, 当前算符: S_0 · S_24, 耗时: 9.331s
[2025-09-12 18:52:08] 自旋算符进度: 26/64, 当前算符: S_0 · S_25, 耗时: 9.305s
[2025-09-12 18:52:18] 自旋算符进度: 27/64, 当前算符: S_0 · S_26, 耗时: 9.330s
[2025-09-12 18:52:27] 自旋算符进度: 28/64, 当前算符: S_0 · S_27, 耗时: 9.314s
[2025-09-12 18:52:36] 自旋算符进度: 29/64, 当前算符: S_0 · S_28, 耗时: 9.330s
[2025-09-12 18:52:45] 自旋算符进度: 30/64, 当前算符: S_0 · S_29, 耗时: 9.328s
[2025-09-12 18:52:55] 自旋算符进度: 31/64, 当前算符: S_0 · S_30, 耗时: 9.332s
[2025-09-12 18:53:04] 自旋算符进度: 32/64, 当前算符: S_0 · S_31, 耗时: 9.315s
[2025-09-12 18:53:14] 自旋算符进度: 33/64, 当前算符: S_0 · S_32, 耗时: 9.339s
[2025-09-12 18:53:23] 自旋算符进度: 34/64, 当前算符: S_0 · S_33, 耗时: 9.314s
[2025-09-12 18:53:32] 自旋算符进度: 35/64, 当前算符: S_0 · S_34, 耗时: 9.326s
[2025-09-12 18:53:41] 自旋算符进度: 36/64, 当前算符: S_0 · S_35, 耗时: 9.311s
[2025-09-12 18:53:51] 自旋算符进度: 37/64, 当前算符: S_0 · S_36, 耗时: 9.317s
[2025-09-12 18:54:00] 自旋算符进度: 38/64, 当前算符: S_0 · S_37, 耗时: 9.331s
[2025-09-12 18:54:09] 自旋算符进度: 39/64, 当前算符: S_0 · S_38, 耗时: 9.331s
[2025-09-12 18:54:19] 自旋算符进度: 40/64, 当前算符: S_0 · S_39, 耗时: 9.317s
[2025-09-12 18:54:28] 自旋算符进度: 41/64, 当前算符: S_0 · S_40, 耗时: 9.328s
[2025-09-12 18:54:37] 自旋算符进度: 42/64, 当前算符: S_0 · S_41, 耗时: 9.356s
[2025-09-12 18:54:47] 自旋算符进度: 43/64, 当前算符: S_0 · S_42, 耗时: 9.328s
[2025-09-12 18:54:56] 自旋算符进度: 44/64, 当前算符: S_0 · S_43, 耗时: 9.322s
[2025-09-12 18:55:05] 自旋算符进度: 45/64, 当前算符: S_0 · S_44, 耗时: 9.328s
[2025-09-12 18:55:15] 自旋算符进度: 46/64, 当前算符: S_0 · S_45, 耗时: 9.329s
[2025-09-12 18:55:24] 自旋算符进度: 47/64, 当前算符: S_0 · S_46, 耗时: 9.326s
[2025-09-12 18:55:33] 自旋算符进度: 48/64, 当前算符: S_0 · S_47, 耗时: 9.324s
[2025-09-12 18:55:43] 自旋算符进度: 49/64, 当前算符: S_0 · S_48, 耗时: 9.323s
[2025-09-12 18:55:52] 自旋算符进度: 50/64, 当前算符: S_0 · S_49, 耗时: 9.328s
[2025-09-12 18:56:01] 自旋算符进度: 51/64, 当前算符: S_0 · S_50, 耗时: 9.329s
[2025-09-12 18:56:11] 自旋算符进度: 52/64, 当前算符: S_0 · S_51, 耗时: 9.328s
[2025-09-12 18:56:20] 自旋算符进度: 53/64, 当前算符: S_0 · S_52, 耗时: 9.329s
[2025-09-12 18:56:30] 自旋算符进度: 54/64, 当前算符: S_0 · S_53, 耗时: 9.349s
[2025-09-12 18:56:39] 自旋算符进度: 55/64, 当前算符: S_0 · S_54, 耗时: 9.321s
[2025-09-12 18:56:48] 自旋算符进度: 56/64, 当前算符: S_0 · S_55, 耗时: 9.327s
[2025-09-12 18:56:58] 自旋算符进度: 57/64, 当前算符: S_0 · S_56, 耗时: 9.329s
[2025-09-12 18:57:07] 自旋算符进度: 58/64, 当前算符: S_0 · S_57, 耗时: 9.326s
[2025-09-12 18:57:16] 自旋算符进度: 59/64, 当前算符: S_0 · S_58, 耗时: 9.303s
[2025-09-12 18:57:25] 自旋算符进度: 60/64, 当前算符: S_0 · S_59, 耗时: 9.314s
[2025-09-12 18:57:35] 自旋算符进度: 61/64, 当前算符: S_0 · S_60, 耗时: 9.328s
[2025-09-12 18:57:44] 自旋算符进度: 62/64, 当前算符: S_0 · S_61, 耗时: 9.331s
[2025-09-12 18:57:53] 自旋算符进度: 63/64, 当前算符: S_0 · S_62, 耗时: 9.312s
[2025-09-12 18:58:03] 自旋算符进度: 64/64, 当前算符: S_0 · S_63, 耗时: 9.286s
[2025-09-12 18:58:03] 自旋相关函数计算完成,总耗时 610.91 秒
[2025-09-12 18:58:04] 计算傅里叶变换...
[2025-09-12 18:58:06] 自旋结构因子计算完成
[2025-09-12 18:58:09] 自旋相关函数平均误差: 0.000687
