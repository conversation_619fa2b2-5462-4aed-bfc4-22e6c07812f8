{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Neel序参量外推分析 (L=4, J2=0)\n", "\n", "本notebook实现以下逻辑：\n", "1. 对L=4，J2=0的每个J1点，取最后四个checkpoint的Neel序参量\n", "2. 对序参量值进行1/iter线性外推，得到无限iter的序参量\n", "3. 绘制每个J1点的外推图（序参量 vs 1/iter）\n", "4. 将所有外推得到的序参量汇总到一张图上（序参量 vs J1）\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import re\n", "from scipy.optimize import curve_fit\n", "\n", "# 设置matplotlib参数\n", "plt.rcParams['font.size'] = 12\n", "plt.rcParams['figure.figsize'] = (10, 6)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def calculate_af_order_parameter(spin_data):\n", "    \"\"\"从spin_data.npy文件中提取Neel反铁磁序参量S(π,π)\"\"\"\n", "    # 提取结构因子和k点信息\n", "    structure_factor = spin_data['structure_factor']\n", "    metadata = spin_data['metadata']\n", "    \n", "    # 获取k点\n", "    L = metadata['L']\n", "    k_points_x = np.linspace(0, 2*np.pi, 2*L, endpoint=False)\n", "    k_points_y = np.linspace(0, 2*np.pi, 2*L, endpoint=False)\n", "    \n", "    # 找到(π,π)点的索引\n", "    pi_idx_x = np.argmin(np.abs(k_points_x - np.pi))\n", "    pi_idx_y = np.argmin(np.abs(k_points_y - np.pi))\n", "    \n", "    # 提取S(π,π)的实部作为Neel序参量\n", "    af_order = structure_factor[pi_idx_y, pi_idx_x].real\n", "    \n", "    return af_order\n", "\n", "def load_checkpoint_data(results_dir, L, J2, J1):\n", "    \"\"\"加载某个(L, J2, J1)配置下所有checkpoint的Neel序参量\"\"\"\n", "    analysis_dir = results_dir / f\"L={L}\" / f\"J2={J2:.2f}\" / f\"J1={J1:.2f}\" / \"analysis\"\n", "    \n", "    if not analysis_dir.exists():\n", "        print(f\"警告: 目录不存在 {analysis_dir}\")\n", "        return None\n", "    \n", "    # 收集所有checkpoint数据\n", "    checkpoint_data = []\n", "    \n", "    for checkpoint_dir in sorted(analysis_dir.iterdir()):\n", "        if not checkpoint_dir.is_dir():\n", "            continue\n", "        \n", "        # 从目录名提取iter数\n", "        iter_match = re.search(r'checkpoint_iter_(\\d+)', checkpoint_dir.name)\n", "        if not iter_match:\n", "            # 可能是final_GCNN\n", "            if 'final' in checkpoint_dir.name:\n", "                # 需要从训练日志读取总iter数\n", "                train_log = analysis_dir.parent / \"training\" / \"train.log\"\n", "                if train_log.exists():\n", "                    with open(train_log, 'r') as f:\n", "                        for line in f:\n", "                            if \"Total iterations:\" in line:\n", "                                total_iter_match = re.search(r'Total iterations:\\s*(\\d+)', line)\n", "                                if total_iter_match:\n", "                                    iter_num = int(total_iter_match.group(1))\n", "                                    break\n", "                        else:\n", "                            continue\n", "                else:\n", "                    continue\n", "            else:\n", "                continue\n", "        else:\n", "            iter_num = int(iter_match.group(1))\n", "        \n", "        # 加载spin数据\n", "        spin_file = checkpoint_dir / \"spin\" / \"spin_data.npy\"\n", "        if not spin_file.exists():\n", "            continue\n", "        \n", "        try:\n", "            spin_data = np.load(spin_file, allow_pickle=True).item()\n", "            af_order = calculate_af_order_parameter(spin_data)\n", "            checkpoint_data.append((iter_num, af_order))\n", "        except Exception as e:\n", "            print(f\"加载 {spin_file} 失败: {e}\")\n", "            continue\n", "    \n", "    if len(checkpoint_data) == 0:\n", "        print(f\"警告: 未找到有效的checkpoint数据 (L={L}, J2={J2}, J1={J1})\")\n", "        return None\n", "    \n", "    # 按iter排序\n", "    checkpoint_data.sort(key=lambda x: x[0])\n", "    \n", "    return checkpoint_data\n", "\n", "def linear_extrapolation(x, y):\n", "    \"\"\"线性外推到x=0 (即1/iter=0, iter=∞)\"\"\"\n", "    # y = a + b*x，外推到x=0即得a\n", "    coeffs = np.polyfit(x, y, 1)\n", "    a, b = coeffs[0], coeffs[1]  # y = a*x + b\n", "    extrapolated_value = b  # 当x=0时，y=b\n", "    \n", "    return extrapolated_value, coeffs\n", "\n", "def plot_extrapolation(iters, af_orders, J1, extrapolated_value, coeffs, save_dir=None):\n", "    \"\"\"绘制单个J1点的外推图\"\"\"\n", "    # 计算1/iter\n", "    inv_iters = 1.0 / np.array(iters)\n", "    \n", "    fig, ax = plt.subplots(figsize=(8, 6))\n", "    \n", "    # 绘制数据点\n", "    ax.plot(inv_iters, af_orders, 'o', markersize=8, label='Checkpoint数据')\n", "    \n", "    # 绘制拟合线\n", "    x_fit = np.linspace(0, max(inv_iters)*1.1, 100)\n", "    y_fit = coeffs[0] * x_fit + coeffs[1]\n", "    ax.plot(x_fit, y_fit, 'r--', linewidth=2, label=f'线性拟合: y={coeffs[0]:.4f}x+{coeffs[1]:.4f}')\n", "    \n", "    # 标记外推值\n", "    ax.plot(0, extrapolated_value, 'r*', markersize=15, \n", "            label=f'外推值 (iter→∞): {extrapolated_value:.6f}')\n", "    \n", "    ax.set_xlabel('1/iter', fontsize=14)\n", "    ax.set_ylabel('Neel序参量 S(π,π)', fontsize=14)\n", "    ax.set_title(f'<PERSON>eel序参量外推 (J1={J1:.2f}, L=4, J2=0)', fontsize=16)\n", "    ax.legend(fontsize=11)\n", "    ax.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    \n", "    if save_dir:\n", "        save_path = save_dir / f\"extrapolation_J1_{J1:.2f}.png\"\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"保存外推图: {save_path}\")\n", "    \n", "    plt.show()\n", "    plt.close()\n", "\n", "def plot_summary(J1_values, extrapolated_values, save_dir=None):\n", "    \"\"\"绘制所有J1点的外推序参量汇总图\"\"\"\n", "    fig, ax = plt.subplots(figsize=(10, 6))\n", "    \n", "    ax.plot(J1_values, extrapolated_values, 'o-', linewidth=2, markersize=8, \n", "            color='#2E86AB', label='外推Neel序参量 (iter→∞)')\n", "    \n", "    ax.set_xlabel('J1', fontsize=14)\n", "    ax.set_ylabel('Neel序参量 S(π,π)', fontsize=14)\n", "    ax.set_title('外推Neel序参量 vs J1 (L=4, J2=0)', fontsize=16)\n", "    ax.legend(fontsize=12)\n", "    ax.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    \n", "    if save_dir:\n", "        save_path = save_dir / \"neel_order_extrapolated_vs_J1.png\"\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"保存汇总图: {save_path}\")\n", "    \n", "    plt.show()\n", "    plt.close()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 主分析流程\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["找到 11 个J1值: [0.0, 0.01, 0.02, 0.03, 0.04, 0.05, 0.06, 0.07, 0.08, 0.09, 0.1]\n"]}], "source": ["# 设置参数\n", "L = 4\n", "J2 = 0.0\n", "results_dir = Path(\"/home/<USER>/Repositories/Shastry-Sutherland_model/results\")\n", "\n", "# 创建输出目录\n", "output_dir = Path(\"/home/<USER>/Repositories/Shastry-Sutherland_model/notebooks/extrapolation_plots\")\n", "output_dir.mkdir(exist_ok=True)\n", "\n", "# 扫描所有J1值\n", "j2_dir = results_dir / f\"L={L}\" / f\"J2={J2:.2f}\"\n", "j1_values = []\n", "for j1_dir in sorted(j2_dir.iterdir()):\n", "    if j1_dir.is_dir() and j1_dir.name.startswith('J1='):\n", "        j1_match = re.search(r'J1=([\\d.]+)', j1_dir.name)\n", "        if j1_match:\n", "            j1_values.append(float(j1_match.group(1)))\n", "\n", "j1_values.sort()\n", "print(f\"找到 {len(j1_values)} 个J1值: {j1_values}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "处理 J1=0.00\n", "============================================================\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.00/analysis/checkpoint_iter_000100/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.00/analysis/checkpoint_iter_000200/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.00/analysis/checkpoint_iter_000300/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.00/analysis/checkpoint_iter_000400/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.00/analysis/checkpoint_iter_000500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.00/analysis/checkpoint_iter_000600/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.00/analysis/checkpoint_iter_000700/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.00/analysis/checkpoint_iter_000800/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.00/analysis/checkpoint_iter_000900/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.00/analysis/checkpoint_iter_001000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.00/analysis/final_GCNN/spin/spin_data.npy 失败: 'L'\n", "警告: 未找到有效的checkpoint数据 (L=4, J2=0.0, J1=0.0)\n", "跳过 J1=0.00: 数据不足（需要至少4个checkpoint）\n", "\n", "============================================================\n", "处理 J1=0.01\n", "============================================================\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.01/analysis/checkpoint_iter_000100/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.01/analysis/checkpoint_iter_000200/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.01/analysis/checkpoint_iter_000300/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.01/analysis/checkpoint_iter_000400/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.01/analysis/checkpoint_iter_000500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.01/analysis/checkpoint_iter_000600/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.01/analysis/checkpoint_iter_000700/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.01/analysis/checkpoint_iter_000800/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.01/analysis/checkpoint_iter_000900/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.01/analysis/checkpoint_iter_001000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.01/analysis/final_GCNN/spin/spin_data.npy 失败: 'L'\n", "警告: 未找到有效的checkpoint数据 (L=4, J2=0.0, J1=0.01)\n", "跳过 J1=0.01: 数据不足（需要至少4个checkpoint）\n", "\n", "============================================================\n", "处理 J1=0.02\n", "============================================================\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.02/analysis/checkpoint_iter_000100/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.02/analysis/checkpoint_iter_000200/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.02/analysis/checkpoint_iter_000300/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.02/analysis/checkpoint_iter_000400/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.02/analysis/checkpoint_iter_000500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.02/analysis/checkpoint_iter_000600/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.02/analysis/checkpoint_iter_000700/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.02/analysis/checkpoint_iter_000800/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.02/analysis/checkpoint_iter_000900/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.02/analysis/checkpoint_iter_001000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.02/analysis/final_GCNN/spin/spin_data.npy 失败: 'L'\n", "警告: 未找到有效的checkpoint数据 (L=4, J2=0.0, J1=0.02)\n", "跳过 J1=0.02: 数据不足（需要至少4个checkpoint）\n", "\n", "============================================================\n", "处理 J1=0.03\n", "============================================================\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.03/analysis/checkpoint_iter_000100/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.03/analysis/checkpoint_iter_000200/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.03/analysis/checkpoint_iter_000300/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.03/analysis/checkpoint_iter_000400/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.03/analysis/checkpoint_iter_000500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.03/analysis/checkpoint_iter_000600/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.03/analysis/checkpoint_iter_000700/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.03/analysis/checkpoint_iter_000800/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.03/analysis/checkpoint_iter_000900/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.03/analysis/checkpoint_iter_001000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.03/analysis/final_GCNN/spin/spin_data.npy 失败: 'L'\n", "警告: 未找到有效的checkpoint数据 (L=4, J2=0.0, J1=0.03)\n", "跳过 J1=0.03: 数据不足（需要至少4个checkpoint）\n", "\n", "============================================================\n", "处理 J1=0.04\n", "============================================================\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.04/analysis/checkpoint_iter_000500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.04/analysis/checkpoint_iter_001000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.04/analysis/checkpoint_iter_001500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.04/analysis/checkpoint_iter_002000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.04/analysis/checkpoint_iter_002500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.04/analysis/checkpoint_iter_003000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.04/analysis/checkpoint_iter_003500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.04/analysis/checkpoint_iter_004000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.04/analysis/checkpoint_iter_004500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.04/analysis/final_GCNN/spin/spin_data.npy 失败: 'L'\n", "警告: 未找到有效的checkpoint数据 (L=4, J2=0.0, J1=0.04)\n", "跳过 J1=0.04: 数据不足（需要至少4个checkpoint）\n", "\n", "============================================================\n", "处理 J1=0.05\n", "============================================================\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.05/analysis/checkpoint_iter_000100/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.05/analysis/checkpoint_iter_000200/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.05/analysis/checkpoint_iter_000300/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.05/analysis/checkpoint_iter_000400/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.05/analysis/checkpoint_iter_000500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.05/analysis/checkpoint_iter_000600/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.05/analysis/checkpoint_iter_000700/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.05/analysis/checkpoint_iter_000800/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.05/analysis/checkpoint_iter_000900/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.05/analysis/checkpoint_iter_001000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.05/analysis/final_GCNN/spin/spin_data.npy 失败: 'L'\n", "警告: 未找到有效的checkpoint数据 (L=4, J2=0.0, J1=0.05)\n", "跳过 J1=0.05: 数据不足（需要至少4个checkpoint）\n", "\n", "============================================================\n", "处理 J1=0.06\n", "============================================================\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.06/analysis/checkpoint_iter_000100/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.06/analysis/checkpoint_iter_000200/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.06/analysis/checkpoint_iter_000300/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.06/analysis/checkpoint_iter_000400/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.06/analysis/checkpoint_iter_000500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.06/analysis/checkpoint_iter_000600/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.06/analysis/checkpoint_iter_000700/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.06/analysis/checkpoint_iter_000800/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.06/analysis/checkpoint_iter_000900/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.06/analysis/checkpoint_iter_001000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.06/analysis/final_GCNN/spin/spin_data.npy 失败: 'L'\n", "警告: 未找到有效的checkpoint数据 (L=4, J2=0.0, J1=0.06)\n", "跳过 J1=0.06: 数据不足（需要至少4个checkpoint）\n", "\n", "============================================================\n", "处理 J1=0.07\n", "============================================================\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.07/analysis/checkpoint_iter_000100/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.07/analysis/checkpoint_iter_000200/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.07/analysis/checkpoint_iter_000300/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.07/analysis/checkpoint_iter_000400/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.07/analysis/checkpoint_iter_000500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.07/analysis/checkpoint_iter_000600/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.07/analysis/checkpoint_iter_000700/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.07/analysis/checkpoint_iter_000800/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.07/analysis/checkpoint_iter_000900/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.07/analysis/checkpoint_iter_001000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.07/analysis/final_GCNN/spin/spin_data.npy 失败: 'L'\n", "警告: 未找到有效的checkpoint数据 (L=4, J2=0.0, J1=0.07)\n", "跳过 J1=0.07: 数据不足（需要至少4个checkpoint）\n", "\n", "============================================================\n", "处理 J1=0.08\n", "============================================================\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.08/analysis/checkpoint_iter_000100/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.08/analysis/checkpoint_iter_000200/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.08/analysis/checkpoint_iter_000300/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.08/analysis/checkpoint_iter_000400/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.08/analysis/checkpoint_iter_000500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.08/analysis/checkpoint_iter_000600/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.08/analysis/checkpoint_iter_000700/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.08/analysis/checkpoint_iter_000800/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.08/analysis/checkpoint_iter_000900/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.08/analysis/checkpoint_iter_001000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.08/analysis/final_GCNN/spin/spin_data.npy 失败: 'L'\n", "警告: 未找到有效的checkpoint数据 (L=4, J2=0.0, J1=0.08)\n", "跳过 J1=0.08: 数据不足（需要至少4个checkpoint）\n", "\n", "============================================================\n", "处理 J1=0.09\n", "============================================================\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.09/analysis/checkpoint_iter_000100/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.09/analysis/checkpoint_iter_000200/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.09/analysis/checkpoint_iter_000300/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.09/analysis/checkpoint_iter_000400/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.09/analysis/checkpoint_iter_000500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.09/analysis/checkpoint_iter_000600/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.09/analysis/checkpoint_iter_000700/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.09/analysis/checkpoint_iter_000800/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.09/analysis/checkpoint_iter_000900/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.09/analysis/checkpoint_iter_001000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.09/analysis/final_GCNN/spin/spin_data.npy 失败: 'L'\n", "警告: 未找到有效的checkpoint数据 (L=4, J2=0.0, J1=0.09)\n", "跳过 J1=0.09: 数据不足（需要至少4个checkpoint）\n", "\n", "============================================================\n", "处理 J1=0.10\n", "============================================================\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.10/analysis/checkpoint_iter_000100/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.10/analysis/checkpoint_iter_000200/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.10/analysis/checkpoint_iter_000300/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.10/analysis/checkpoint_iter_000400/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.10/analysis/checkpoint_iter_000500/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.10/analysis/checkpoint_iter_000600/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.10/analysis/checkpoint_iter_000700/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.10/analysis/checkpoint_iter_000800/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.10/analysis/checkpoint_iter_000900/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.10/analysis/checkpoint_iter_001000/spin/spin_data.npy 失败: 'L'\n", "加载 /home/<USER>/Repositories/Shastry-Sutherland_model/results/L=4/J2=0.00/J1=0.10/analysis/final_GCNN/spin/spin_data.npy 失败: 'L'\n", "警告: 未找到有效的checkpoint数据 (L=4, J2=0.0, J1=0.1)\n", "跳过 J1=0.10: 数据不足（需要至少4个checkpoint）\n"]}], "source": ["# 对每个J1进行分析\n", "extrapolation_results = {}\n", "\n", "for J1 in j1_values:\n", "    print(f\"\\n{'='*60}\")\n", "    print(f\"处理 J1={J1:.2f}\")\n", "    print('='*60)\n", "    \n", "    # 加载checkpoint数据\n", "    checkpoint_data = load_checkpoint_data(results_dir, L, J2, J1)\n", "    \n", "    if checkpoint_data is None or len(checkpoint_data) < 4:\n", "        print(f\"跳过 J1={J1:.2f}: 数据不足（需要至少4个checkpoint）\")\n", "        continue\n", "    \n", "    print(f\"找到 {len(checkpoint_data)} 个checkpoint\")\n", "    \n", "    # 取最后4个checkpoint\n", "    last_four = checkpoint_data[-4:]\n", "    iters = [x[0] for x in last_four]\n", "    af_orders = [x[1] for x in last_four]\n", "    \n", "    print(f\"使用最后4个checkpoint:\")\n", "    for iter_num, af_order in last_four:\n", "        print(f\"  iter={iter_num:6d}, S(π,π)={af_order:.6f}\")\n", "    \n", "    # 计算1/iter\n", "    inv_iters = 1.0 / np.array(iters)\n", "    \n", "    # 线性外推\n", "    extrapolated_value, coeffs = linear_extrapolation(inv_iters, af_orders)\n", "    \n", "    print(f\"\\n外推结果:\")\n", "    print(f\"  拟合方程: y = {coeffs[0]:.4f} * (1/iter) + {coeffs[1]:.4f}\")\n", "    print(f\"  外推值 (iter→∞): {extrapolated_value:.6f}\")\n", "    \n", "    # 保存结果\n", "    extrapolation_results[J1] = {\n", "        'iters': iters,\n", "        'af_orders': af_orders,\n", "        'extrapolated_value': extrapolated_value,\n", "        'coeffs': coeffs\n", "    }\n", "    \n", "    # 绘制外推图\n", "    plot_extrapolation(iters, af_orders, J1, extrapolated_value, coeffs, output_dir)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 汇总图\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "汇总结果\n", "============================================================\n", "J1         外推Neel序参量           \n", "------------------------------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_2451715/247595316.py:137: UserWarning: Glyph 24207 (\\N{CJK UNIFIED IDEOGRAPH-5E8F}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/tmp/ipykernel_2451715/247595316.py:137: UserWarning: Glyph 21442 (\\N{CJK UNIFIED IDEOGRAPH-53C2}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/tmp/ipykernel_2451715/247595316.py:137: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/tmp/ipykernel_2451715/247595316.py:137: UserWarning: Glyph 22806 (\\N{CJK UNIFIED IDEOGRAPH-5916}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/tmp/ipykernel_2451715/247595316.py:137: UserWarning: Glyph 25512 (\\N{CJK UNIFIED IDEOGRAPH-63A8}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/tmp/ipykernel_2451715/247595316.py:141: UserWarning: Glyph 24207 (\\N{CJK UNIFIED IDEOGRAPH-5E8F}) missing from font(s) DejaVu Sans.\n", "  plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "/tmp/ipykernel_2451715/247595316.py:141: UserWarning: Glyph 21442 (\\N{CJK UNIFIED IDEOGRAPH-53C2}) missing from font(s) DejaVu Sans.\n", "  plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "/tmp/ipykernel_2451715/247595316.py:141: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) DejaVu Sans.\n", "  plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "/tmp/ipykernel_2451715/247595316.py:141: UserWarning: Glyph 22806 (\\N{CJK UNIFIED IDEOGRAPH-5916}) missing from font(s) DejaVu Sans.\n", "  plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "/tmp/ipykernel_2451715/247595316.py:141: UserWarning: Glyph 25512 (\\N{CJK UNIFIED IDEOGRAPH-63A8}) missing from font(s) DejaVu Sans.\n", "  plt.savefig(save_path, dpi=300, bbox_inches='tight')\n"]}, {"name": "stdout", "output_type": "stream", "text": ["保存汇总图: /home/<USER>/Repositories/Shastry-<PERSON>_model/notebooks/extrapolation_plots/neel_order_extrapolated_vs_J1.png\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 24207 (\\N{CJK UNIFIED IDEOGRAPH-5E8F}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 21442 (\\N{CJK UNIFIED IDEOGRAPH-53C2}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 37327 (\\N{CJK UNIFIED IDEOGRAPH-91CF}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 22806 (\\N{CJK UNIFIED IDEOGRAPH-5916}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/home/<USER>/.conda/envs/netket/lib/python3.12/site-packages/IPython/core/pylabtools.py:170: UserWarning: Glyph 25512 (\\N{CJK UNIFIED IDEOGRAPH-63A8}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 准备汇总数据\n", "summary_j1 = []\n", "summary_extrapolated = []\n", "\n", "for J1 in sorted(extrapolation_results.keys()):\n", "    summary_j1.append(J1)\n", "    summary_extrapolated.append(extrapolation_results[J1]['extrapolated_value'])\n", "\n", "print(f\"\\n{'='*60}\")\n", "print(\"汇总结果\")\n", "print('='*60)\n", "print(f\"{'J1':<10} {'外推Neel序参量':<20}\")\n", "print('-'*60)\n", "for j1, extrap in zip(summary_j1, summary_extrapolated):\n", "    print(f\"{j1:<10.2f} {extrap:<20.6f}\")\n", "\n", "# 绘制汇总图\n", "plot_summary(summary_j1, summary_extrapolated, output_dir)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "数值结果已保存到: /home/<USER>/Repositories/Shastry-Sutherland_model/notebooks/extrapolation_plots/extrapolation_results.txt\n"]}], "source": ["# 保存数值结果到文件\n", "output_file = output_dir / \"extrapolation_results.txt\"\n", "with open(output_file, 'w') as f:\n", "    f.write(\"Neel序参量外推结果 (L=4, J2=0)\\n\")\n", "    f.write(\"=\"*60 + \"\\n\")\n", "    f.write(f\"{'J1':<10} {'外推值':<20} {'拟合斜率':<20} {'拟合截距':<20}\\n\")\n", "    f.write(\"-\"*60 + \"\\n\")\n", "    for J1 in sorted(extrapolation_results.keys()):\n", "        result = extrapolation_results[J1]\n", "        f.write(f\"{J1:<10.2f} {result['extrapolated_value']:<20.6f} \"\n", "                f\"{result['coeffs'][0]:<20.6f} {result['coeffs'][1]:<20.6f}\\n\")\n", "\n", "print(f\"\\n数值结果已保存到: {output_file}\")\n"]}], "metadata": {"kernelspec": {"display_name": "netket", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 2}