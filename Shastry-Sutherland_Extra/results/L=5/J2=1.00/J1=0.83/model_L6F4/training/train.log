[2025-10-07 16:57:17] ✓ 从checkpoint恢复: results/L=5/J2=1.00/J1=0.82/model_L6F4/training/checkpoints/final_GCNN.pkl
[2025-10-07 16:57:17]   - 迭代次数: final
[2025-10-07 16:57:17]   - 能量: -46.173199+0.000422j ± 0.006774, Var: 0.187958
[2025-10-07 16:57:17]   - 时间戳: 2025-10-07T16:56:55.932047+08:00
[2025-10-07 16:57:39] ✓ 变分状态参数已从checkpoint恢复
[2025-10-07 16:57:39] ✓ 从final状态恢复, 重置迭代计数为0
[2025-10-07 16:57:39] ======================================================================================================
[2025-10-07 16:57:39] GCNN for Shastry-Sutherland Model
[2025-10-07 16:57:39] ======================================================================================================
[2025-10-07 16:57:39] System parameters:
[2025-10-07 16:57:39]   - System size: L=5, N=100
[2025-10-07 16:57:39]   - System parameters: J1=0.83, J2=1.0, Q=0.0
[2025-10-07 16:57:39] ------------------------------------------------------------------------------------------------------
[2025-10-07 16:57:39] Model parameters:
[2025-10-07 16:57:39]   - Number of layers = 6
[2025-10-07 16:57:39]   - Number of features = 4
[2025-10-07 16:57:39]   - Total parameters = 32444
[2025-10-07 16:57:39] ------------------------------------------------------------------------------------------------------
[2025-10-07 16:57:39] Training parameters:
[2025-10-07 16:57:39]   - Total iterations: 1050
[2025-10-07 16:57:39]   - Annealing cycles: 3
[2025-10-07 16:57:39]   - Initial period: 150
[2025-10-07 16:57:39]   - Period multiplier: 2.0
[2025-10-07 16:57:39]   - LR range: 0.005 - 0.03 (cosine annealing)
[2025-10-07 16:57:39]   - Samples: 4096
[2025-10-07 16:57:39]   - Discarded samples: 0
[2025-10-07 16:57:39]   - Chunk size: 4096
[2025-10-07 16:57:39]   - Diagonal shift: 0.15
[2025-10-07 16:57:39]   - Gradient clipping: 1.0
[2025-10-07 16:57:39]   - Checkpoint enabled: interval=100
[2025-10-07 16:57:39]   - Checkpoint directory: results/L=5/J2=1.00/J1=0.83/model_L6F4/training/checkpoints
[2025-10-07 16:57:39] ------------------------------------------------------------------------------------------------------
[2025-10-07 16:57:39] Device status:
[2025-10-07 16:57:39]   - Devices model: NVIDIA H200 NVL
[2025-10-07 16:57:39]   - Number of devices: 1
[2025-10-07 16:57:39]   - Sharding: True
[2025-10-07 16:57:40] ======================================================================================================
[2025-10-07 16:58:20] [Iter    1/1050] R0[0/150]     | LR: 0.030000 | E:  -46.816712 | E_var:     0.4442 | E_err:   0.010413
[2025-10-07 16:58:47] [Iter    2/1050] R0[1/150]     | LR: 0.029997 | E:  -46.825360 | E_var:     0.2454 | E_err:   0.007740
[2025-10-07 16:58:55] [Iter    3/1050] R0[2/150]     | LR: 0.029989 | E:  -46.835122 | E_var:     0.2708 | E_err:   0.008131
[2025-10-07 16:59:02] [Iter    4/1050] R0[3/150]     | LR: 0.029975 | E:  -46.824798 | E_var:     0.2063 | E_err:   0.007098
[2025-10-07 16:59:10] [Iter    5/1050] R0[4/150]     | LR: 0.029956 | E:  -46.821640 | E_var:     0.2290 | E_err:   0.007477
[2025-10-07 16:59:18] [Iter    6/1050] R0[5/150]     | LR: 0.029932 | E:  -46.817954 | E_var:     0.4583 | E_err:   0.010578
[2025-10-07 16:59:26] [Iter    7/1050] R0[6/150]     | LR: 0.029901 | E:  -46.822193 | E_var:     0.2005 | E_err:   0.006997
[2025-10-07 16:59:34] [Iter    8/1050] R0[7/150]     | LR: 0.029866 | E:  -46.823369 | E_var:     0.2039 | E_err:   0.007056
[2025-10-07 16:59:41] [Iter    9/1050] R0[8/150]     | LR: 0.029825 | E:  -46.810091 | E_var:     0.2560 | E_err:   0.007906
[2025-10-07 16:59:49] [Iter   10/1050] R0[9/150]     | LR: 0.029779 | E:  -46.806663 | E_var:     0.2297 | E_err:   0.007489
[2025-10-07 16:59:57] [Iter   11/1050] R0[10/150]    | LR: 0.029727 | E:  -46.812409 | E_var:     0.1658 | E_err:   0.006362
[2025-10-07 17:00:05] [Iter   12/1050] R0[11/150]    | LR: 0.029670 | E:  -46.822495 | E_var:     0.2370 | E_err:   0.007607
[2025-10-07 17:00:13] [Iter   13/1050] R0[12/150]    | LR: 0.029607 | E:  -46.804185 | E_var:     0.3680 | E_err:   0.009478
[2025-10-07 17:00:20] [Iter   14/1050] R0[13/150]    | LR: 0.029540 | E:  -46.809698 | E_var:     0.1820 | E_err:   0.006666
[2025-10-07 17:00:28] [Iter   15/1050] R0[14/150]    | LR: 0.029466 | E:  -46.826503 | E_var:     0.2326 | E_err:   0.007536
[2025-10-07 17:00:36] [Iter   16/1050] R0[15/150]    | LR: 0.029388 | E:  -46.801591 | E_var:     0.2271 | E_err:   0.007447
[2025-10-07 17:00:44] [Iter   17/1050] R0[16/150]    | LR: 0.029305 | E:  -46.806941 | E_var:     0.1855 | E_err:   0.006730
[2025-10-07 17:00:52] [Iter   18/1050] R0[17/150]    | LR: 0.029216 | E:  -46.802368 | E_var:     0.1777 | E_err:   0.006588
[2025-10-07 17:00:59] [Iter   19/1050] R0[18/150]    | LR: 0.029122 | E:  -46.800008 | E_var:     0.1982 | E_err:   0.006956
[2025-10-07 17:01:07] [Iter   20/1050] R0[19/150]    | LR: 0.029023 | E:  -46.816273 | E_var:     0.1797 | E_err:   0.006623
[2025-10-07 17:01:15] [Iter   21/1050] R0[20/150]    | LR: 0.028919 | E:  -46.805106 | E_var:     0.1782 | E_err:   0.006597
[2025-10-07 17:01:23] [Iter   22/1050] R0[21/150]    | LR: 0.028810 | E:  -46.817967 | E_var:     0.1802 | E_err:   0.006634
[2025-10-07 17:01:31] [Iter   23/1050] R0[22/150]    | LR: 0.028696 | E:  -46.803893 | E_var:     0.1711 | E_err:   0.006463
[2025-10-07 17:01:38] [Iter   24/1050] R0[23/150]    | LR: 0.028578 | E:  -46.830023 | E_var:     0.2294 | E_err:   0.007483
[2025-10-07 17:01:46] [Iter   25/1050] R0[24/150]    | LR: 0.028454 | E:  -46.822319 | E_var:     0.1842 | E_err:   0.006705
[2025-10-07 17:01:54] [Iter   26/1050] R0[25/150]    | LR: 0.028325 | E:  -46.807905 | E_var:     0.2732 | E_err:   0.008167
[2025-10-07 17:02:02] [Iter   27/1050] R0[26/150]    | LR: 0.028192 | E:  -46.811783 | E_var:     0.1693 | E_err:   0.006429
[2025-10-07 17:02:10] [Iter   28/1050] R0[27/150]    | LR: 0.028054 | E:  -46.827082 | E_var:     0.2126 | E_err:   0.007204
[2025-10-07 17:02:17] [Iter   29/1050] R0[28/150]    | LR: 0.027912 | E:  -46.812265 | E_var:     0.1659 | E_err:   0.006365
[2025-10-07 17:02:25] [Iter   30/1050] R0[29/150]    | LR: 0.027764 | E:  -46.816746 | E_var:     0.1776 | E_err:   0.006586
[2025-10-07 17:02:33] [Iter   31/1050] R0[30/150]    | LR: 0.027613 | E:  -46.813780 | E_var:     0.1646 | E_err:   0.006339
[2025-10-07 17:02:41] [Iter   32/1050] R0[31/150]    | LR: 0.027457 | E:  -46.808431 | E_var:     0.1900 | E_err:   0.006811
[2025-10-07 17:02:49] [Iter   33/1050] R0[32/150]    | LR: 0.027296 | E:  -46.808959 | E_var:     0.1400 | E_err:   0.005847
[2025-10-07 17:02:56] [Iter   34/1050] R0[33/150]    | LR: 0.027131 | E:  -46.811528 | E_var:     0.2272 | E_err:   0.007448
[2025-10-07 17:03:04] [Iter   35/1050] R0[34/150]    | LR: 0.026962 | E:  -46.811483 | E_var:     0.1463 | E_err:   0.005977
[2025-10-07 17:03:12] [Iter   36/1050] R0[35/150]    | LR: 0.026789 | E:  -46.812114 | E_var:     0.1551 | E_err:   0.006153
[2025-10-07 17:03:20] [Iter   37/1050] R0[36/150]    | LR: 0.026612 | E:  -46.802061 | E_var:     0.1684 | E_err:   0.006412
[2025-10-07 17:03:28] [Iter   38/1050] R0[37/150]    | LR: 0.026431 | E:  -46.812060 | E_var:     0.1717 | E_err:   0.006474
[2025-10-07 17:03:35] [Iter   39/1050] R0[38/150]    | LR: 0.026246 | E:  -46.812970 | E_var:     0.2536 | E_err:   0.007869
[2025-10-07 17:03:43] [Iter   40/1050] R0[39/150]    | LR: 0.026057 | E:  -46.821055 | E_var:     0.1906 | E_err:   0.006821
[2025-10-07 17:03:51] [Iter   41/1050] R0[40/150]    | LR: 0.025864 | E:  -46.806972 | E_var:     0.3493 | E_err:   0.009234
[2025-10-07 17:03:59] [Iter   42/1050] R0[41/150]    | LR: 0.025668 | E:  -46.804026 | E_var:     0.1668 | E_err:   0.006381
[2025-10-07 17:04:07] [Iter   43/1050] R0[42/150]    | LR: 0.025468 | E:  -46.822474 | E_var:     0.1717 | E_err:   0.006474
[2025-10-07 17:04:14] [Iter   44/1050] R0[43/150]    | LR: 0.025264 | E:  -46.809611 | E_var:     0.1872 | E_err:   0.006760
[2025-10-07 17:04:22] [Iter   45/1050] R0[44/150]    | LR: 0.025057 | E:  -46.802114 | E_var:     0.1950 | E_err:   0.006900
[2025-10-07 17:04:30] [Iter   46/1050] R0[45/150]    | LR: 0.024847 | E:  -46.803541 | E_var:     0.1966 | E_err:   0.006928
[2025-10-07 17:04:38] [Iter   47/1050] R0[46/150]    | LR: 0.024634 | E:  -46.814203 | E_var:     0.1325 | E_err:   0.005687
[2025-10-07 17:04:45] [Iter   48/1050] R0[47/150]    | LR: 0.024417 | E:  -46.808058 | E_var:     0.1818 | E_err:   0.006663
[2025-10-07 17:04:53] [Iter   49/1050] R0[48/150]    | LR: 0.024198 | E:  -46.800198 | E_var:     0.2296 | E_err:   0.007487
[2025-10-07 17:05:01] [Iter   50/1050] R0[49/150]    | LR: 0.023975 | E:  -46.800053 | E_var:     0.1967 | E_err:   0.006929
[2025-10-07 17:05:09] [Iter   51/1050] R0[50/150]    | LR: 0.023750 | E:  -46.799430 | E_var:     0.1970 | E_err:   0.006935
[2025-10-07 17:05:17] [Iter   52/1050] R0[51/150]    | LR: 0.023522 | E:  -46.825953 | E_var:     0.1938 | E_err:   0.006879
[2025-10-07 17:05:24] [Iter   53/1050] R0[52/150]    | LR: 0.023291 | E:  -46.798523 | E_var:     0.1808 | E_err:   0.006644
[2025-10-07 17:05:32] [Iter   54/1050] R0[53/150]    | LR: 0.023058 | E:  -46.802178 | E_var:     0.1558 | E_err:   0.006167
[2025-10-07 17:05:40] [Iter   55/1050] R0[54/150]    | LR: 0.022822 | E:  -46.811733 | E_var:     0.1423 | E_err:   0.005894
[2025-10-07 17:05:48] [Iter   56/1050] R0[55/150]    | LR: 0.022584 | E:  -46.817344 | E_var:     0.1521 | E_err:   0.006094
[2025-10-07 17:05:56] [Iter   57/1050] R0[56/150]    | LR: 0.022344 | E:  -46.812071 | E_var:     0.2048 | E_err:   0.007071
[2025-10-07 17:06:03] [Iter   58/1050] R0[57/150]    | LR: 0.022102 | E:  -46.808671 | E_var:     0.1789 | E_err:   0.006608
[2025-10-07 17:06:11] [Iter   59/1050] R0[58/150]    | LR: 0.021857 | E:  -46.804164 | E_var:     0.1632 | E_err:   0.006313
[2025-10-07 17:06:19] [Iter   60/1050] R0[59/150]    | LR: 0.021611 | E:  -46.808607 | E_var:     0.2238 | E_err:   0.007391
[2025-10-07 17:06:27] [Iter   61/1050] R0[60/150]    | LR: 0.021363 | E:  -46.803054 | E_var:     0.1628 | E_err:   0.006304
[2025-10-07 17:06:35] [Iter   62/1050] R0[61/150]    | LR: 0.021113 | E:  -46.803019 | E_var:     0.1578 | E_err:   0.006207
[2025-10-07 17:06:42] [Iter   63/1050] R0[62/150]    | LR: 0.020861 | E:  -46.812856 | E_var:     0.1756 | E_err:   0.006548
[2025-10-07 17:06:50] [Iter   64/1050] R0[63/150]    | LR: 0.020609 | E:  -46.822098 | E_var:     0.2425 | E_err:   0.007694
[2025-10-07 17:06:58] [Iter   65/1050] R0[64/150]    | LR: 0.020354 | E:  -46.805907 | E_var:     0.1589 | E_err:   0.006229
[2025-10-07 17:07:06] [Iter   66/1050] R0[65/150]    | LR: 0.020099 | E:  -46.804136 | E_var:     0.1971 | E_err:   0.006937
[2025-10-07 17:07:14] [Iter   67/1050] R0[66/150]    | LR: 0.019842 | E:  -46.806887 | E_var:     0.1723 | E_err:   0.006487
[2025-10-07 17:07:21] [Iter   68/1050] R0[67/150]    | LR: 0.019585 | E:  -46.805258 | E_var:     0.1592 | E_err:   0.006234
[2025-10-07 17:07:29] [Iter   69/1050] R0[68/150]    | LR: 0.019326 | E:  -46.810822 | E_var:     0.1217 | E_err:   0.005451
[2025-10-07 17:07:37] [Iter   70/1050] R0[69/150]    | LR: 0.019067 | E:  -46.807344 | E_var:     0.1765 | E_err:   0.006564
[2025-10-07 17:07:45] [Iter   71/1050] R0[70/150]    | LR: 0.018807 | E:  -46.812930 | E_var:     0.1737 | E_err:   0.006512
[2025-10-07 17:07:53] [Iter   72/1050] R0[71/150]    | LR: 0.018546 | E:  -46.815495 | E_var:     0.1942 | E_err:   0.006886
[2025-10-07 17:08:00] [Iter   73/1050] R0[72/150]    | LR: 0.018285 | E:  -46.819512 | E_var:     0.4257 | E_err:   0.010195
[2025-10-07 17:08:08] [Iter   74/1050] R0[73/150]    | LR: 0.018023 | E:  -46.813729 | E_var:     0.1616 | E_err:   0.006281
[2025-10-07 17:08:16] [Iter   75/1050] R0[74/150]    | LR: 0.017762 | E:  -46.810783 | E_var:     0.1656 | E_err:   0.006358
[2025-10-07 17:08:24] [Iter   76/1050] R0[75/150]    | LR: 0.017500 | E:  -46.817313 | E_var:     0.2094 | E_err:   0.007149
[2025-10-07 17:08:31] [Iter   77/1050] R0[76/150]    | LR: 0.017238 | E:  -46.809443 | E_var:     0.1801 | E_err:   0.006630
[2025-10-07 17:08:39] [Iter   78/1050] R0[77/150]    | LR: 0.016977 | E:  -46.800296 | E_var:     0.1504 | E_err:   0.006060
[2025-10-07 17:08:47] [Iter   79/1050] R0[78/150]    | LR: 0.016715 | E:  -46.813855 | E_var:     0.1938 | E_err:   0.006878
[2025-10-07 17:08:55] [Iter   80/1050] R0[79/150]    | LR: 0.016454 | E:  -46.808369 | E_var:     0.2112 | E_err:   0.007180
[2025-10-07 17:09:03] [Iter   81/1050] R0[80/150]    | LR: 0.016193 | E:  -46.811622 | E_var:     0.3076 | E_err:   0.008666
[2025-10-07 17:09:10] [Iter   82/1050] R0[81/150]    | LR: 0.015933 | E:  -46.822275 | E_var:     0.1898 | E_err:   0.006807
[2025-10-07 17:09:18] [Iter   83/1050] R0[82/150]    | LR: 0.015674 | E:  -46.808934 | E_var:     0.1544 | E_err:   0.006139
[2025-10-07 17:09:26] [Iter   84/1050] R0[83/150]    | LR: 0.015415 | E:  -46.821841 | E_var:     0.2779 | E_err:   0.008237
[2025-10-07 17:09:34] [Iter   85/1050] R0[84/150]    | LR: 0.015158 | E:  -46.803473 | E_var:     0.1584 | E_err:   0.006219
[2025-10-07 17:09:42] [Iter   86/1050] R0[85/150]    | LR: 0.014901 | E:  -46.811649 | E_var:     0.1813 | E_err:   0.006653
[2025-10-07 17:09:49] [Iter   87/1050] R0[86/150]    | LR: 0.014646 | E:  -46.815280 | E_var:     0.1532 | E_err:   0.006116
[2025-10-07 17:09:57] [Iter   88/1050] R0[87/150]    | LR: 0.014391 | E:  -46.817795 | E_var:     0.1742 | E_err:   0.006522
[2025-10-07 17:10:05] [Iter   89/1050] R0[88/150]    | LR: 0.014139 | E:  -46.812177 | E_var:     0.1884 | E_err:   0.006782
[2025-10-07 17:10:13] [Iter   90/1050] R0[89/150]    | LR: 0.013887 | E:  -46.812221 | E_var:     0.1641 | E_err:   0.006330
[2025-10-07 17:10:21] [Iter   91/1050] R0[90/150]    | LR: 0.013637 | E:  -46.834690 | E_var:     0.2500 | E_err:   0.007813
[2025-10-07 17:10:28] [Iter   92/1050] R0[91/150]    | LR: 0.013389 | E:  -46.803996 | E_var:     0.1425 | E_err:   0.005898
[2025-10-07 17:10:36] [Iter   93/1050] R0[92/150]    | LR: 0.013143 | E:  -46.811853 | E_var:     0.1865 | E_err:   0.006748
[2025-10-07 17:10:44] [Iter   94/1050] R0[93/150]    | LR: 0.012898 | E:  -46.810462 | E_var:     0.1956 | E_err:   0.006911
[2025-10-07 17:10:52] [Iter   95/1050] R0[94/150]    | LR: 0.012656 | E:  -46.808327 | E_var:     0.1781 | E_err:   0.006595
[2025-10-07 17:10:59] [Iter   96/1050] R0[95/150]    | LR: 0.012416 | E:  -46.809783 | E_var:     0.1998 | E_err:   0.006984
[2025-10-07 17:11:07] [Iter   97/1050] R0[96/150]    | LR: 0.012178 | E:  -46.802360 | E_var:     0.2034 | E_err:   0.007047
[2025-10-07 17:11:15] [Iter   98/1050] R0[97/150]    | LR: 0.011942 | E:  -46.805977 | E_var:     0.1410 | E_err:   0.005867
[2025-10-07 17:11:23] [Iter   99/1050] R0[98/150]    | LR: 0.011709 | E:  -46.807337 | E_var:     0.1565 | E_err:   0.006181
[2025-10-07 17:11:31] [Iter  100/1050] R0[99/150]    | LR: 0.011478 | E:  -46.812237 | E_var:     0.1532 | E_err:   0.006115
[2025-10-07 17:11:31] ✓ Checkpoint saved: checkpoint_iter_000100.pkl
[2025-10-07 17:11:38] [Iter  101/1050] R0[100/150]   | LR: 0.011250 | E:  -46.807821 | E_var:     0.1823 | E_err:   0.006671
[2025-10-07 17:11:46] [Iter  102/1050] R0[101/150]   | LR: 0.011025 | E:  -46.800883 | E_var:     0.2017 | E_err:   0.007017
[2025-10-07 17:11:54] [Iter  103/1050] R0[102/150]   | LR: 0.010802 | E:  -46.806479 | E_var:     0.1723 | E_err:   0.006486
[2025-10-07 17:12:02] [Iter  104/1050] R0[103/150]   | LR: 0.010583 | E:  -46.801956 | E_var:     0.1689 | E_err:   0.006421
[2025-10-07 17:12:10] [Iter  105/1050] R0[104/150]   | LR: 0.010366 | E:  -46.791804 | E_var:     0.1461 | E_err:   0.005973
[2025-10-07 17:12:17] [Iter  106/1050] R0[105/150]   | LR: 0.010153 | E:  -46.805000 | E_var:     0.2956 | E_err:   0.008496
[2025-10-07 17:12:25] [Iter  107/1050] R0[106/150]   | LR: 0.009943 | E:  -46.811805 | E_var:     0.1613 | E_err:   0.006275
[2025-10-07 17:12:33] [Iter  108/1050] R0[107/150]   | LR: 0.009736 | E:  -46.815755 | E_var:     0.1710 | E_err:   0.006461
[2025-10-07 17:12:41] [Iter  109/1050] R0[108/150]   | LR: 0.009532 | E:  -46.808810 | E_var:     0.1550 | E_err:   0.006152
[2025-10-07 17:12:49] [Iter  110/1050] R0[109/150]   | LR: 0.009332 | E:  -46.805044 | E_var:     0.1657 | E_err:   0.006361
[2025-10-07 17:12:56] [Iter  111/1050] R0[110/150]   | LR: 0.009136 | E:  -46.819947 | E_var:     0.2079 | E_err:   0.007124
[2025-10-07 17:13:04] [Iter  112/1050] R0[111/150]   | LR: 0.008943 | E:  -46.793393 | E_var:     0.1534 | E_err:   0.006120
[2025-10-07 17:13:12] [Iter  113/1050] R0[112/150]   | LR: 0.008754 | E:  -46.812687 | E_var:     0.1967 | E_err:   0.006929
[2025-10-07 17:13:20] [Iter  114/1050] R0[113/150]   | LR: 0.008569 | E:  -46.810306 | E_var:     0.1709 | E_err:   0.006459
[2025-10-07 17:13:27] [Iter  115/1050] R0[114/150]   | LR: 0.008388 | E:  -46.808240 | E_var:     0.2234 | E_err:   0.007385
[2025-10-07 17:13:35] [Iter  116/1050] R0[115/150]   | LR: 0.008211 | E:  -46.811616 | E_var:     0.1833 | E_err:   0.006689
[2025-10-07 17:13:43] [Iter  117/1050] R0[116/150]   | LR: 0.008038 | E:  -46.813990 | E_var:     0.1988 | E_err:   0.006966
[2025-10-07 17:13:51] [Iter  118/1050] R0[117/150]   | LR: 0.007869 | E:  -46.808413 | E_var:     0.2165 | E_err:   0.007270
[2025-10-07 17:13:59] [Iter  119/1050] R0[118/150]   | LR: 0.007704 | E:  -46.811277 | E_var:     0.1360 | E_err:   0.005763
[2025-10-07 17:14:06] [Iter  120/1050] R0[119/150]   | LR: 0.007543 | E:  -46.814289 | E_var:     0.1903 | E_err:   0.006816
[2025-10-07 17:14:14] [Iter  121/1050] R0[120/150]   | LR: 0.007387 | E:  -46.811422 | E_var:     0.2029 | E_err:   0.007038
[2025-10-07 17:14:22] [Iter  122/1050] R0[121/150]   | LR: 0.007236 | E:  -46.808610 | E_var:     0.2647 | E_err:   0.008040
[2025-10-07 17:14:30] [Iter  123/1050] R0[122/150]   | LR: 0.007088 | E:  -46.801457 | E_var:     0.1751 | E_err:   0.006537
[2025-10-07 17:14:38] [Iter  124/1050] R0[123/150]   | LR: 0.006946 | E:  -46.806287 | E_var:     0.1880 | E_err:   0.006775
[2025-10-07 17:14:45] [Iter  125/1050] R0[124/150]   | LR: 0.006808 | E:  -46.798471 | E_var:     0.1858 | E_err:   0.006735
[2025-10-07 17:14:53] [Iter  126/1050] R0[125/150]   | LR: 0.006675 | E:  -46.803224 | E_var:     0.2101 | E_err:   0.007162
[2025-10-07 17:15:01] [Iter  127/1050] R0[126/150]   | LR: 0.006546 | E:  -46.816814 | E_var:     0.1645 | E_err:   0.006338
[2025-10-07 17:15:09] [Iter  128/1050] R0[127/150]   | LR: 0.006422 | E:  -46.811575 | E_var:     0.1832 | E_err:   0.006688
[2025-10-07 17:15:17] [Iter  129/1050] R0[128/150]   | LR: 0.006304 | E:  -46.801629 | E_var:     0.1880 | E_err:   0.006775
[2025-10-07 17:15:24] [Iter  130/1050] R0[129/150]   | LR: 0.006190 | E:  -46.807746 | E_var:     0.1885 | E_err:   0.006783
[2025-10-07 17:15:32] [Iter  131/1050] R0[130/150]   | LR: 0.006081 | E:  -46.803506 | E_var:     0.2376 | E_err:   0.007616
[2025-10-07 17:15:40] [Iter  132/1050] R0[131/150]   | LR: 0.005977 | E:  -46.793560 | E_var:     0.1609 | E_err:   0.006268
[2025-10-07 17:15:48] [Iter  133/1050] R0[132/150]   | LR: 0.005878 | E:  -46.813446 | E_var:     0.2074 | E_err:   0.007115
[2025-10-07 17:15:55] [Iter  134/1050] R0[133/150]   | LR: 0.005784 | E:  -46.806315 | E_var:     0.1872 | E_err:   0.006760
[2025-10-07 17:16:03] [Iter  135/1050] R0[134/150]   | LR: 0.005695 | E:  -46.823286 | E_var:     0.4789 | E_err:   0.010813
[2025-10-07 17:16:11] [Iter  136/1050] R0[135/150]   | LR: 0.005612 | E:  -46.804549 | E_var:     0.1822 | E_err:   0.006670
[2025-10-07 17:16:19] [Iter  137/1050] R0[136/150]   | LR: 0.005534 | E:  -46.804953 | E_var:     0.2319 | E_err:   0.007525
[2025-10-07 17:16:27] [Iter  138/1050] R0[137/150]   | LR: 0.005460 | E:  -46.805725 | E_var:     0.1654 | E_err:   0.006355
[2025-10-07 17:16:34] [Iter  139/1050] R0[138/150]   | LR: 0.005393 | E:  -46.830112 | E_var:     0.8741 | E_err:   0.014608
[2025-10-07 17:16:42] [Iter  140/1050] R0[139/150]   | LR: 0.005330 | E:  -46.816988 | E_var:     0.1488 | E_err:   0.006027
[2025-10-07 17:16:50] [Iter  141/1050] R0[140/150]   | LR: 0.005273 | E:  -46.800129 | E_var:     0.2258 | E_err:   0.007425
[2025-10-07 17:16:58] [Iter  142/1050] R0[141/150]   | LR: 0.005221 | E:  -46.803798 | E_var:     0.1931 | E_err:   0.006866
[2025-10-07 17:17:06] [Iter  143/1050] R0[142/150]   | LR: 0.005175 | E:  -46.807906 | E_var:     0.2262 | E_err:   0.007431
[2025-10-07 17:17:13] [Iter  144/1050] R0[143/150]   | LR: 0.005134 | E:  -46.800968 | E_var:     0.1562 | E_err:   0.006175
[2025-10-07 17:17:21] [Iter  145/1050] R0[144/150]   | LR: 0.005099 | E:  -46.815358 | E_var:     0.2489 | E_err:   0.007795
[2025-10-07 17:17:29] [Iter  146/1050] R0[145/150]   | LR: 0.005068 | E:  -46.817918 | E_var:     0.1778 | E_err:   0.006588
[2025-10-07 17:17:37] [Iter  147/1050] R0[146/150]   | LR: 0.005044 | E:  -46.810230 | E_var:     0.1811 | E_err:   0.006649
[2025-10-07 17:17:44] [Iter  148/1050] R0[147/150]   | LR: 0.005025 | E:  -46.798954 | E_var:     0.1588 | E_err:   0.006226
[2025-10-07 17:17:52] [Iter  149/1050] R0[148/150]   | LR: 0.005011 | E:  -46.814351 | E_var:     0.1854 | E_err:   0.006729
[2025-10-07 17:18:00] [Iter  150/1050] R0[149/150]   | LR: 0.005003 | E:  -46.812623 | E_var:     0.2031 | E_err:   0.007041
[2025-10-07 17:18:00] 🔄 RESTART #1 | Period: 300
[2025-10-07 17:18:08] [Iter  151/1050] R1[0/300]     | LR: 0.030000 | E:  -46.803295 | E_var:     0.1925 | E_err:   0.006855
[2025-10-07 17:18:16] [Iter  152/1050] R1[1/300]     | LR: 0.029999 | E:  -46.804364 | E_var:     0.1700 | E_err:   0.006443
[2025-10-07 17:18:23] [Iter  153/1050] R1[2/300]     | LR: 0.029997 | E:  -46.808373 | E_var:     0.1711 | E_err:   0.006463
[2025-10-07 17:18:31] [Iter  154/1050] R1[3/300]     | LR: 0.029994 | E:  -46.809398 | E_var:     0.1841 | E_err:   0.006705
[2025-10-07 17:18:39] [Iter  155/1050] R1[4/300]     | LR: 0.029989 | E:  -46.811948 | E_var:     0.2079 | E_err:   0.007124
[2025-10-07 17:18:47] [Iter  156/1050] R1[5/300]     | LR: 0.029983 | E:  -46.809850 | E_var:     0.1783 | E_err:   0.006597
[2025-10-07 17:18:55] [Iter  157/1050] R1[6/300]     | LR: 0.029975 | E:  -46.804215 | E_var:     0.1610 | E_err:   0.006269
[2025-10-07 17:19:02] [Iter  158/1050] R1[7/300]     | LR: 0.029966 | E:  -46.804332 | E_var:     0.1578 | E_err:   0.006207
[2025-10-07 17:19:10] [Iter  159/1050] R1[8/300]     | LR: 0.029956 | E:  -46.804301 | E_var:     0.1626 | E_err:   0.006301
[2025-10-07 17:19:18] [Iter  160/1050] R1[9/300]     | LR: 0.029945 | E:  -46.815248 | E_var:     0.1627 | E_err:   0.006303
[2025-10-07 17:19:26] [Iter  161/1050] R1[10/300]    | LR: 0.029932 | E:  -46.802208 | E_var:     0.1580 | E_err:   0.006212
[2025-10-07 17:19:34] [Iter  162/1050] R1[11/300]    | LR: 0.029917 | E:  -46.816002 | E_var:     0.2700 | E_err:   0.008118
[2025-10-07 17:19:41] [Iter  163/1050] R1[12/300]    | LR: 0.029901 | E:  -46.797833 | E_var:     0.2750 | E_err:   0.008194
[2025-10-07 17:19:49] [Iter  164/1050] R1[13/300]    | LR: 0.029884 | E:  -46.815384 | E_var:     0.1965 | E_err:   0.006926
[2025-10-07 17:19:57] [Iter  165/1050] R1[14/300]    | LR: 0.029866 | E:  -46.806530 | E_var:     0.1985 | E_err:   0.006961
[2025-10-07 17:20:05] [Iter  166/1050] R1[15/300]    | LR: 0.029846 | E:  -46.816425 | E_var:     0.2091 | E_err:   0.007146
[2025-10-07 17:20:12] [Iter  167/1050] R1[16/300]    | LR: 0.029825 | E:  -46.800185 | E_var:     0.1559 | E_err:   0.006169
[2025-10-07 17:20:20] [Iter  168/1050] R1[17/300]    | LR: 0.029802 | E:  -46.814671 | E_var:     0.1710 | E_err:   0.006461
[2025-10-07 17:20:28] [Iter  169/1050] R1[18/300]    | LR: 0.029779 | E:  -46.816956 | E_var:     0.1517 | E_err:   0.006086
[2025-10-07 17:20:36] [Iter  170/1050] R1[19/300]    | LR: 0.029753 | E:  -46.809163 | E_var:     0.1419 | E_err:   0.005886
[2025-10-07 17:20:44] [Iter  171/1050] R1[20/300]    | LR: 0.029727 | E:  -46.807308 | E_var:     0.1702 | E_err:   0.006446
[2025-10-07 17:20:51] [Iter  172/1050] R1[21/300]    | LR: 0.029699 | E:  -46.811786 | E_var:     0.2086 | E_err:   0.007136
[2025-10-07 17:20:59] [Iter  173/1050] R1[22/300]    | LR: 0.029670 | E:  -46.809891 | E_var:     0.2229 | E_err:   0.007378
[2025-10-07 17:21:07] [Iter  174/1050] R1[23/300]    | LR: 0.029639 | E:  -46.811416 | E_var:     0.1833 | E_err:   0.006689
[2025-10-07 17:21:15] [Iter  175/1050] R1[24/300]    | LR: 0.029607 | E:  -46.811882 | E_var:     0.1874 | E_err:   0.006765
[2025-10-07 17:21:23] [Iter  176/1050] R1[25/300]    | LR: 0.029574 | E:  -46.808080 | E_var:     0.2146 | E_err:   0.007238
[2025-10-07 17:21:30] [Iter  177/1050] R1[26/300]    | LR: 0.029540 | E:  -46.813267 | E_var:     0.1414 | E_err:   0.005876
[2025-10-07 17:21:38] [Iter  178/1050] R1[27/300]    | LR: 0.029504 | E:  -46.808372 | E_var:     0.1524 | E_err:   0.006100
[2025-10-07 17:21:46] [Iter  179/1050] R1[28/300]    | LR: 0.029466 | E:  -46.821571 | E_var:     0.1554 | E_err:   0.006159
[2025-10-07 17:21:54] [Iter  180/1050] R1[29/300]    | LR: 0.029428 | E:  -46.805882 | E_var:     0.2000 | E_err:   0.006988
[2025-10-07 17:22:02] [Iter  181/1050] R1[30/300]    | LR: 0.029388 | E:  -46.801284 | E_var:     0.2125 | E_err:   0.007202
[2025-10-07 17:22:09] [Iter  182/1050] R1[31/300]    | LR: 0.029347 | E:  -46.816152 | E_var:     0.1518 | E_err:   0.006087
[2025-10-07 17:22:17] [Iter  183/1050] R1[32/300]    | LR: 0.029305 | E:  -46.823263 | E_var:     0.1896 | E_err:   0.006803
[2025-10-07 17:22:25] [Iter  184/1050] R1[33/300]    | LR: 0.029261 | E:  -46.804716 | E_var:     0.1490 | E_err:   0.006032
[2025-10-07 17:22:33] [Iter  185/1050] R1[34/300]    | LR: 0.029216 | E:  -46.826376 | E_var:     0.1525 | E_err:   0.006102
[2025-10-07 17:22:40] [Iter  186/1050] R1[35/300]    | LR: 0.029170 | E:  -46.813231 | E_var:     0.1761 | E_err:   0.006557
[2025-10-07 17:22:48] [Iter  187/1050] R1[36/300]    | LR: 0.029122 | E:  -46.810087 | E_var:     0.1482 | E_err:   0.006016
[2025-10-07 17:22:56] [Iter  188/1050] R1[37/300]    | LR: 0.029073 | E:  -46.820420 | E_var:     0.1665 | E_err:   0.006375
[2025-10-07 17:23:04] [Iter  189/1050] R1[38/300]    | LR: 0.029023 | E:  -46.806237 | E_var:     0.1447 | E_err:   0.005945
[2025-10-07 17:23:12] [Iter  190/1050] R1[39/300]    | LR: 0.028972 | E:  -46.818404 | E_var:     0.2068 | E_err:   0.007105
[2025-10-07 17:23:19] [Iter  191/1050] R1[40/300]    | LR: 0.028919 | E:  -46.815470 | E_var:     0.2539 | E_err:   0.007873
[2025-10-07 17:23:27] [Iter  192/1050] R1[41/300]    | LR: 0.028865 | E:  -46.803152 | E_var:     0.1937 | E_err:   0.006877
[2025-10-07 17:23:35] [Iter  193/1050] R1[42/300]    | LR: 0.028810 | E:  -46.814709 | E_var:     0.1938 | E_err:   0.006879
[2025-10-07 17:23:43] [Iter  194/1050] R1[43/300]    | LR: 0.028754 | E:  -46.817550 | E_var:     0.1689 | E_err:   0.006421
[2025-10-07 17:23:51] [Iter  195/1050] R1[44/300]    | LR: 0.028696 | E:  -46.812061 | E_var:     0.2062 | E_err:   0.007095
[2025-10-07 17:23:58] [Iter  196/1050] R1[45/300]    | LR: 0.028638 | E:  -46.814353 | E_var:     0.1635 | E_err:   0.006318
[2025-10-07 17:24:06] [Iter  197/1050] R1[46/300]    | LR: 0.028578 | E:  -46.822216 | E_var:     0.1710 | E_err:   0.006461
[2025-10-07 17:24:14] [Iter  198/1050] R1[47/300]    | LR: 0.028516 | E:  -46.810690 | E_var:     0.1519 | E_err:   0.006090
[2025-10-07 17:24:22] [Iter  199/1050] R1[48/300]    | LR: 0.028454 | E:  -46.807613 | E_var:     0.1895 | E_err:   0.006801
[2025-10-07 17:24:29] [Iter  200/1050] R1[49/300]    | LR: 0.028390 | E:  -46.803125 | E_var:     0.1662 | E_err:   0.006369
[2025-10-07 17:24:30] ✓ Checkpoint saved: checkpoint_iter_000200.pkl
[2025-10-07 17:24:37] [Iter  201/1050] R1[50/300]    | LR: 0.028325 | E:  -46.820950 | E_var:     0.1694 | E_err:   0.006430
[2025-10-07 17:24:45] [Iter  202/1050] R1[51/300]    | LR: 0.028259 | E:  -46.806311 | E_var:     0.1529 | E_err:   0.006110
[2025-10-07 17:24:53] [Iter  203/1050] R1[52/300]    | LR: 0.028192 | E:  -46.812535 | E_var:     0.1640 | E_err:   0.006328
[2025-10-07 17:25:01] [Iter  204/1050] R1[53/300]    | LR: 0.028124 | E:  -46.809815 | E_var:     0.1595 | E_err:   0.006240
[2025-10-07 17:25:08] [Iter  205/1050] R1[54/300]    | LR: 0.028054 | E:  -46.802772 | E_var:     0.1602 | E_err:   0.006254
[2025-10-07 17:25:16] [Iter  206/1050] R1[55/300]    | LR: 0.027983 | E:  -46.808382 | E_var:     0.1429 | E_err:   0.005907
[2025-10-07 17:25:24] [Iter  207/1050] R1[56/300]    | LR: 0.027912 | E:  -46.809611 | E_var:     0.2285 | E_err:   0.007468
[2025-10-07 17:25:32] [Iter  208/1050] R1[57/300]    | LR: 0.027839 | E:  -46.807576 | E_var:     0.1634 | E_err:   0.006316
[2025-10-07 17:25:40] [Iter  209/1050] R1[58/300]    | LR: 0.027764 | E:  -46.811024 | E_var:     0.1566 | E_err:   0.006184
[2025-10-07 17:25:47] [Iter  210/1050] R1[59/300]    | LR: 0.027689 | E:  -46.823844 | E_var:     0.2515 | E_err:   0.007836
[2025-10-07 17:25:55] [Iter  211/1050] R1[60/300]    | LR: 0.027613 | E:  -46.823825 | E_var:     0.1767 | E_err:   0.006568
[2025-10-07 17:26:03] [Iter  212/1050] R1[61/300]    | LR: 0.027535 | E:  -46.820289 | E_var:     0.1941 | E_err:   0.006884
[2025-10-07 17:26:11] [Iter  213/1050] R1[62/300]    | LR: 0.027457 | E:  -46.811500 | E_var:     0.1540 | E_err:   0.006132
[2025-10-07 17:26:19] [Iter  214/1050] R1[63/300]    | LR: 0.027377 | E:  -46.813034 | E_var:     0.1771 | E_err:   0.006576
[2025-10-07 17:26:26] [Iter  215/1050] R1[64/300]    | LR: 0.027296 | E:  -46.803749 | E_var:     0.2201 | E_err:   0.007331
[2025-10-07 17:26:34] [Iter  216/1050] R1[65/300]    | LR: 0.027214 | E:  -46.815994 | E_var:     0.1826 | E_err:   0.006676
[2025-10-07 17:26:42] [Iter  217/1050] R1[66/300]    | LR: 0.027131 | E:  -46.813136 | E_var:     0.1712 | E_err:   0.006465
[2025-10-07 17:26:50] [Iter  218/1050] R1[67/300]    | LR: 0.027047 | E:  -46.809592 | E_var:     0.1803 | E_err:   0.006635
[2025-10-07 17:26:58] [Iter  219/1050] R1[68/300]    | LR: 0.026962 | E:  -46.811363 | E_var:     0.2148 | E_err:   0.007242
[2025-10-07 17:27:05] [Iter  220/1050] R1[69/300]    | LR: 0.026876 | E:  -46.802181 | E_var:     0.2189 | E_err:   0.007310
[2025-10-07 17:27:13] [Iter  221/1050] R1[70/300]    | LR: 0.026789 | E:  -46.813547 | E_var:     0.2239 | E_err:   0.007394
[2025-10-07 17:27:21] [Iter  222/1050] R1[71/300]    | LR: 0.026701 | E:  -46.810902 | E_var:     0.1470 | E_err:   0.005990
[2025-10-07 17:27:29] [Iter  223/1050] R1[72/300]    | LR: 0.026612 | E:  -46.807878 | E_var:     0.2337 | E_err:   0.007553
[2025-10-07 17:27:36] [Iter  224/1050] R1[73/300]    | LR: 0.026522 | E:  -46.813880 | E_var:     0.1671 | E_err:   0.006387
[2025-10-07 17:27:44] [Iter  225/1050] R1[74/300]    | LR: 0.026431 | E:  -46.811209 | E_var:     0.1566 | E_err:   0.006184
[2025-10-07 17:27:52] [Iter  226/1050] R1[75/300]    | LR: 0.026339 | E:  -46.808984 | E_var:     0.1689 | E_err:   0.006422
[2025-10-07 17:28:00] [Iter  227/1050] R1[76/300]    | LR: 0.026246 | E:  -46.814166 | E_var:     0.2243 | E_err:   0.007401
[2025-10-07 17:28:08] [Iter  228/1050] R1[77/300]    | LR: 0.026152 | E:  -46.805149 | E_var:     0.1841 | E_err:   0.006704
[2025-10-07 17:28:15] [Iter  229/1050] R1[78/300]    | LR: 0.026057 | E:  -46.808217 | E_var:     0.1515 | E_err:   0.006082
[2025-10-07 17:28:23] [Iter  230/1050] R1[79/300]    | LR: 0.025961 | E:  -46.817635 | E_var:     0.1869 | E_err:   0.006754
[2025-10-07 17:28:31] [Iter  231/1050] R1[80/300]    | LR: 0.025864 | E:  -46.805327 | E_var:     0.1801 | E_err:   0.006631
[2025-10-07 17:28:39] [Iter  232/1050] R1[81/300]    | LR: 0.025766 | E:  -46.803076 | E_var:     0.3052 | E_err:   0.008631
[2025-10-07 17:28:47] [Iter  233/1050] R1[82/300]    | LR: 0.025668 | E:  -46.820212 | E_var:     0.1836 | E_err:   0.006695
[2025-10-07 17:28:54] [Iter  234/1050] R1[83/300]    | LR: 0.025568 | E:  -46.811374 | E_var:     0.1714 | E_err:   0.006469
[2025-10-07 17:29:02] [Iter  235/1050] R1[84/300]    | LR: 0.025468 | E:  -46.816945 | E_var:     0.1531 | E_err:   0.006113
[2025-10-07 17:29:10] [Iter  236/1050] R1[85/300]    | LR: 0.025367 | E:  -46.798484 | E_var:     0.1917 | E_err:   0.006840
[2025-10-07 17:29:18] [Iter  237/1050] R1[86/300]    | LR: 0.025264 | E:  -46.804427 | E_var:     0.2605 | E_err:   0.007974
[2025-10-07 17:29:26] [Iter  238/1050] R1[87/300]    | LR: 0.025161 | E:  -46.802414 | E_var:     0.1712 | E_err:   0.006465
[2025-10-07 17:29:33] [Iter  239/1050] R1[88/300]    | LR: 0.025057 | E:  -46.814744 | E_var:     0.1524 | E_err:   0.006100
[2025-10-07 17:29:41] [Iter  240/1050] R1[89/300]    | LR: 0.024953 | E:  -46.810805 | E_var:     0.1663 | E_err:   0.006371
[2025-10-07 17:29:49] [Iter  241/1050] R1[90/300]    | LR: 0.024847 | E:  -46.810615 | E_var:     0.1880 | E_err:   0.006776
[2025-10-07 17:29:57] [Iter  242/1050] R1[91/300]    | LR: 0.024741 | E:  -46.818928 | E_var:     0.2227 | E_err:   0.007374
[2025-10-07 17:30:04] [Iter  243/1050] R1[92/300]    | LR: 0.024634 | E:  -46.813549 | E_var:     0.1630 | E_err:   0.006309
[2025-10-07 17:30:12] [Iter  244/1050] R1[93/300]    | LR: 0.024526 | E:  -46.815645 | E_var:     0.1688 | E_err:   0.006420
[2025-10-07 17:30:20] [Iter  245/1050] R1[94/300]    | LR: 0.024417 | E:  -46.819260 | E_var:     0.1518 | E_err:   0.006087
[2025-10-07 17:30:28] [Iter  246/1050] R1[95/300]    | LR: 0.024308 | E:  -46.810248 | E_var:     0.1718 | E_err:   0.006477
[2025-10-07 17:30:36] [Iter  247/1050] R1[96/300]    | LR: 0.024198 | E:  -46.819483 | E_var:     0.1689 | E_err:   0.006421
[2025-10-07 17:30:43] [Iter  248/1050] R1[97/300]    | LR: 0.024087 | E:  -46.808494 | E_var:     0.1601 | E_err:   0.006253
[2025-10-07 17:30:51] [Iter  249/1050] R1[98/300]    | LR: 0.023975 | E:  -46.811419 | E_var:     0.1596 | E_err:   0.006242
[2025-10-07 17:30:59] [Iter  250/1050] R1[99/300]    | LR: 0.023863 | E:  -46.803043 | E_var:     0.1655 | E_err:   0.006356
[2025-10-07 17:31:07] [Iter  251/1050] R1[100/300]   | LR: 0.023750 | E:  -46.816165 | E_var:     0.1555 | E_err:   0.006161
[2025-10-07 17:31:15] [Iter  252/1050] R1[101/300]   | LR: 0.023636 | E:  -46.811213 | E_var:     0.1737 | E_err:   0.006513
[2025-10-07 17:31:22] [Iter  253/1050] R1[102/300]   | LR: 0.023522 | E:  -46.808857 | E_var:     0.1749 | E_err:   0.006535
[2025-10-07 17:31:30] [Iter  254/1050] R1[103/300]   | LR: 0.023407 | E:  -46.813105 | E_var:     0.1800 | E_err:   0.006629
[2025-10-07 17:31:38] [Iter  255/1050] R1[104/300]   | LR: 0.023291 | E:  -46.814335 | E_var:     0.1996 | E_err:   0.006981
[2025-10-07 17:31:46] [Iter  256/1050] R1[105/300]   | LR: 0.023175 | E:  -46.810550 | E_var:     0.1544 | E_err:   0.006139
[2025-10-07 17:31:54] [Iter  257/1050] R1[106/300]   | LR: 0.023058 | E:  -46.822354 | E_var:     0.1500 | E_err:   0.006052
[2025-10-07 17:32:01] [Iter  258/1050] R1[107/300]   | LR: 0.022940 | E:  -46.802020 | E_var:     0.2410 | E_err:   0.007671
[2025-10-07 17:32:09] [Iter  259/1050] R1[108/300]   | LR: 0.022822 | E:  -46.813456 | E_var:     0.1798 | E_err:   0.006625
[2025-10-07 17:32:17] [Iter  260/1050] R1[109/300]   | LR: 0.022704 | E:  -46.807012 | E_var:     0.2645 | E_err:   0.008036
[2025-10-07 17:32:25] [Iter  261/1050] R1[110/300]   | LR: 0.022584 | E:  -46.815753 | E_var:     0.1611 | E_err:   0.006271
[2025-10-07 17:32:32] [Iter  262/1050] R1[111/300]   | LR: 0.022464 | E:  -46.804686 | E_var:     0.1512 | E_err:   0.006076
[2025-10-07 17:32:40] [Iter  263/1050] R1[112/300]   | LR: 0.022344 | E:  -46.800835 | E_var:     0.1395 | E_err:   0.005836
[2025-10-07 17:32:48] [Iter  264/1050] R1[113/300]   | LR: 0.022223 | E:  -46.811809 | E_var:     0.1806 | E_err:   0.006641
[2025-10-07 17:32:56] [Iter  265/1050] R1[114/300]   | LR: 0.022102 | E:  -46.809875 | E_var:     0.1518 | E_err:   0.006087
[2025-10-07 17:33:04] [Iter  266/1050] R1[115/300]   | LR: 0.021980 | E:  -46.803366 | E_var:     0.1698 | E_err:   0.006439
[2025-10-07 17:33:11] [Iter  267/1050] R1[116/300]   | LR: 0.021857 | E:  -46.817736 | E_var:     0.1924 | E_err:   0.006854
[2025-10-07 17:33:19] [Iter  268/1050] R1[117/300]   | LR: 0.021734 | E:  -46.804970 | E_var:     0.1642 | E_err:   0.006332
[2025-10-07 17:33:27] [Iter  269/1050] R1[118/300]   | LR: 0.021611 | E:  -46.807821 | E_var:     0.1478 | E_err:   0.006008
[2025-10-07 17:33:35] [Iter  270/1050] R1[119/300]   | LR: 0.021487 | E:  -46.816174 | E_var:     0.1750 | E_err:   0.006537
[2025-10-07 17:33:43] [Iter  271/1050] R1[120/300]   | LR: 0.021363 | E:  -46.815934 | E_var:     0.1602 | E_err:   0.006254
[2025-10-07 17:33:50] [Iter  272/1050] R1[121/300]   | LR: 0.021238 | E:  -46.811427 | E_var:     0.1910 | E_err:   0.006828
[2025-10-07 17:33:58] [Iter  273/1050] R1[122/300]   | LR: 0.021113 | E:  -46.808599 | E_var:     0.2022 | E_err:   0.007026
[2025-10-07 17:34:06] [Iter  274/1050] R1[123/300]   | LR: 0.020987 | E:  -46.812237 | E_var:     0.2283 | E_err:   0.007465
[2025-10-07 17:34:14] [Iter  275/1050] R1[124/300]   | LR: 0.020861 | E:  -46.817585 | E_var:     0.1646 | E_err:   0.006339
[2025-10-07 17:34:22] [Iter  276/1050] R1[125/300]   | LR: 0.020735 | E:  -46.801870 | E_var:     0.6821 | E_err:   0.012905
[2025-10-07 17:34:29] [Iter  277/1050] R1[126/300]   | LR: 0.020609 | E:  -46.818966 | E_var:     0.1599 | E_err:   0.006249
[2025-10-07 17:34:37] [Iter  278/1050] R1[127/300]   | LR: 0.020482 | E:  -46.810679 | E_var:     0.1716 | E_err:   0.006472
[2025-10-07 17:34:45] [Iter  279/1050] R1[128/300]   | LR: 0.020354 | E:  -46.808937 | E_var:     0.1756 | E_err:   0.006548
[2025-10-07 17:34:53] [Iter  280/1050] R1[129/300]   | LR: 0.020227 | E:  -46.802451 | E_var:     0.1593 | E_err:   0.006237
[2025-10-07 17:35:00] [Iter  281/1050] R1[130/300]   | LR: 0.020099 | E:  -46.809519 | E_var:     0.1621 | E_err:   0.006290
[2025-10-07 17:35:08] [Iter  282/1050] R1[131/300]   | LR: 0.019971 | E:  -46.812208 | E_var:     0.1536 | E_err:   0.006124
[2025-10-07 17:35:16] [Iter  283/1050] R1[132/300]   | LR: 0.019842 | E:  -46.814738 | E_var:     0.2012 | E_err:   0.007008
[2025-10-07 17:35:24] [Iter  284/1050] R1[133/300]   | LR: 0.019714 | E:  -46.813737 | E_var:     0.3569 | E_err:   0.009334
[2025-10-07 17:35:32] [Iter  285/1050] R1[134/300]   | LR: 0.019585 | E:  -46.804241 | E_var:     0.1459 | E_err:   0.005969
[2025-10-07 17:35:39] [Iter  286/1050] R1[135/300]   | LR: 0.019455 | E:  -46.811403 | E_var:     0.1815 | E_err:   0.006656
[2025-10-07 17:35:47] [Iter  287/1050] R1[136/300]   | LR: 0.019326 | E:  -46.815581 | E_var:     0.1719 | E_err:   0.006478
[2025-10-07 17:35:55] [Iter  288/1050] R1[137/300]   | LR: 0.019196 | E:  -46.797053 | E_var:     0.1768 | E_err:   0.006570
[2025-10-07 17:36:03] [Iter  289/1050] R1[138/300]   | LR: 0.019067 | E:  -46.812780 | E_var:     0.1746 | E_err:   0.006530
[2025-10-07 17:36:11] [Iter  290/1050] R1[139/300]   | LR: 0.018937 | E:  -46.803001 | E_var:     0.1673 | E_err:   0.006390
[2025-10-07 17:36:18] [Iter  291/1050] R1[140/300]   | LR: 0.018807 | E:  -46.805242 | E_var:     0.1964 | E_err:   0.006924
[2025-10-07 17:36:26] [Iter  292/1050] R1[141/300]   | LR: 0.018676 | E:  -46.806861 | E_var:     0.1711 | E_err:   0.006464
[2025-10-07 17:36:34] [Iter  293/1050] R1[142/300]   | LR: 0.018546 | E:  -46.821121 | E_var:     0.1606 | E_err:   0.006261
[2025-10-07 17:36:42] [Iter  294/1050] R1[143/300]   | LR: 0.018415 | E:  -46.809792 | E_var:     0.1889 | E_err:   0.006791
[2025-10-07 17:36:50] [Iter  295/1050] R1[144/300]   | LR: 0.018285 | E:  -46.813777 | E_var:     0.1512 | E_err:   0.006075
[2025-10-07 17:36:58] [Iter  296/1050] R1[145/300]   | LR: 0.018154 | E:  -46.814641 | E_var:     0.1796 | E_err:   0.006622
[2025-10-07 17:37:06] [Iter  297/1050] R1[146/300]   | LR: 0.018023 | E:  -46.826081 | E_var:     0.2703 | E_err:   0.008124
[2025-10-07 17:37:13] [Iter  298/1050] R1[147/300]   | LR: 0.017893 | E:  -46.808526 | E_var:     0.1397 | E_err:   0.005841
[2025-10-07 17:37:21] [Iter  299/1050] R1[148/300]   | LR: 0.017762 | E:  -46.801373 | E_var:     0.1992 | E_err:   0.006974
[2025-10-07 17:37:29] [Iter  300/1050] R1[149/300]   | LR: 0.017631 | E:  -46.812742 | E_var:     0.1437 | E_err:   0.005923
[2025-10-07 17:37:29] ✓ Checkpoint saved: checkpoint_iter_000300.pkl
[2025-10-07 17:37:37] [Iter  301/1050] R1[150/300]   | LR: 0.017500 | E:  -46.813183 | E_var:     0.1658 | E_err:   0.006362
[2025-10-07 17:37:45] [Iter  302/1050] R1[151/300]   | LR: 0.017369 | E:  -46.819864 | E_var:     0.1823 | E_err:   0.006671
[2025-10-07 17:37:52] [Iter  303/1050] R1[152/300]   | LR: 0.017238 | E:  -46.810963 | E_var:     0.1354 | E_err:   0.005750
[2025-10-07 17:38:00] [Iter  304/1050] R1[153/300]   | LR: 0.017107 | E:  -46.794823 | E_var:     0.1748 | E_err:   0.006533
[2025-10-07 17:38:08] [Iter  305/1050] R1[154/300]   | LR: 0.016977 | E:  -46.805378 | E_var:     0.1502 | E_err:   0.006055
[2025-10-07 17:38:16] [Iter  306/1050] R1[155/300]   | LR: 0.016846 | E:  -46.812006 | E_var:     0.1630 | E_err:   0.006308
[2025-10-07 17:38:24] [Iter  307/1050] R1[156/300]   | LR: 0.016715 | E:  -46.826836 | E_var:     0.1731 | E_err:   0.006500
[2025-10-07 17:38:31] [Iter  308/1050] R1[157/300]   | LR: 0.016585 | E:  -46.818054 | E_var:     0.1586 | E_err:   0.006222
[2025-10-07 17:38:39] [Iter  309/1050] R1[158/300]   | LR: 0.016454 | E:  -46.812647 | E_var:     0.1529 | E_err:   0.006110
[2025-10-07 17:38:47] [Iter  310/1050] R1[159/300]   | LR: 0.016324 | E:  -46.809115 | E_var:     0.1741 | E_err:   0.006519
[2025-10-07 17:38:55] [Iter  311/1050] R1[160/300]   | LR: 0.016193 | E:  -46.823182 | E_var:     0.2399 | E_err:   0.007653
[2025-10-07 17:39:03] [Iter  312/1050] R1[161/300]   | LR: 0.016063 | E:  -46.814624 | E_var:     0.1372 | E_err:   0.005787
[2025-10-07 17:39:11] [Iter  313/1050] R1[162/300]   | LR: 0.015933 | E:  -46.814679 | E_var:     0.1781 | E_err:   0.006594
[2025-10-07 17:39:19] [Iter  314/1050] R1[163/300]   | LR: 0.015804 | E:  -46.813957 | E_var:     0.1540 | E_err:   0.006131
[2025-10-07 17:39:27] [Iter  315/1050] R1[164/300]   | LR: 0.015674 | E:  -46.818334 | E_var:     0.1518 | E_err:   0.006087
[2025-10-07 17:39:34] [Iter  316/1050] R1[165/300]   | LR: 0.015545 | E:  -46.806093 | E_var:     0.2739 | E_err:   0.008178
[2025-10-07 17:39:42] [Iter  317/1050] R1[166/300]   | LR: 0.015415 | E:  -46.806417 | E_var:     0.1919 | E_err:   0.006845
[2025-10-07 17:39:50] [Iter  318/1050] R1[167/300]   | LR: 0.015286 | E:  -46.808222 | E_var:     0.1720 | E_err:   0.006480
[2025-10-07 17:39:58] [Iter  319/1050] R1[168/300]   | LR: 0.015158 | E:  -46.789375 | E_var:     0.1883 | E_err:   0.006780
[2025-10-07 17:40:06] [Iter  320/1050] R1[169/300]   | LR: 0.015029 | E:  -46.802874 | E_var:     0.1643 | E_err:   0.006333
[2025-10-07 17:40:13] [Iter  321/1050] R1[170/300]   | LR: 0.014901 | E:  -46.810009 | E_var:     0.1248 | E_err:   0.005519
[2025-10-07 17:40:21] [Iter  322/1050] R1[171/300]   | LR: 0.014773 | E:  -46.813683 | E_var:     0.1670 | E_err:   0.006385
[2025-10-07 17:40:29] [Iter  323/1050] R1[172/300]   | LR: 0.014646 | E:  -46.817438 | E_var:     0.2129 | E_err:   0.007210
[2025-10-07 17:40:37] [Iter  324/1050] R1[173/300]   | LR: 0.014518 | E:  -46.825605 | E_var:     0.2483 | E_err:   0.007786
[2025-10-07 17:40:45] [Iter  325/1050] R1[174/300]   | LR: 0.014391 | E:  -46.807032 | E_var:     0.1597 | E_err:   0.006243
[2025-10-07 17:40:52] [Iter  326/1050] R1[175/300]   | LR: 0.014265 | E:  -46.818129 | E_var:     0.1321 | E_err:   0.005678
[2025-10-07 17:41:00] [Iter  327/1050] R1[176/300]   | LR: 0.014139 | E:  -46.817487 | E_var:     0.1544 | E_err:   0.006139
[2025-10-07 17:41:08] [Iter  328/1050] R1[177/300]   | LR: 0.014013 | E:  -46.802544 | E_var:     0.1760 | E_err:   0.006554
[2025-10-07 17:41:16] [Iter  329/1050] R1[178/300]   | LR: 0.013887 | E:  -46.808323 | E_var:     0.1532 | E_err:   0.006116
[2025-10-07 17:41:24] [Iter  330/1050] R1[179/300]   | LR: 0.013762 | E:  -46.822447 | E_var:     0.1393 | E_err:   0.005831
[2025-10-07 17:41:31] [Iter  331/1050] R1[180/300]   | LR: 0.013637 | E:  -46.811564 | E_var:     0.1856 | E_err:   0.006731
[2025-10-07 17:41:39] [Iter  332/1050] R1[181/300]   | LR: 0.013513 | E:  -46.803228 | E_var:     0.1638 | E_err:   0.006325
[2025-10-07 17:41:47] [Iter  333/1050] R1[182/300]   | LR: 0.013389 | E:  -46.818783 | E_var:     0.1907 | E_err:   0.006824
[2025-10-07 17:41:55] [Iter  334/1050] R1[183/300]   | LR: 0.013266 | E:  -46.806220 | E_var:     0.2081 | E_err:   0.007128
[2025-10-07 17:42:03] [Iter  335/1050] R1[184/300]   | LR: 0.013143 | E:  -46.813916 | E_var:     0.1405 | E_err:   0.005856
[2025-10-07 17:42:10] [Iter  336/1050] R1[185/300]   | LR: 0.013020 | E:  -46.798368 | E_var:     0.3772 | E_err:   0.009597
[2025-10-07 17:42:18] [Iter  337/1050] R1[186/300]   | LR: 0.012898 | E:  -46.814668 | E_var:     0.1748 | E_err:   0.006532
[2025-10-07 17:42:26] [Iter  338/1050] R1[187/300]   | LR: 0.012777 | E:  -46.809788 | E_var:     0.2175 | E_err:   0.007286
[2025-10-07 17:42:34] [Iter  339/1050] R1[188/300]   | LR: 0.012656 | E:  -46.809229 | E_var:     0.1357 | E_err:   0.005757
[2025-10-07 17:42:42] [Iter  340/1050] R1[189/300]   | LR: 0.012536 | E:  -46.808920 | E_var:     0.1826 | E_err:   0.006676
[2025-10-07 17:42:49] [Iter  341/1050] R1[190/300]   | LR: 0.012416 | E:  -46.800106 | E_var:     0.1603 | E_err:   0.006255
[2025-10-07 17:42:57] [Iter  342/1050] R1[191/300]   | LR: 0.012296 | E:  -46.808934 | E_var:     0.1673 | E_err:   0.006392
[2025-10-07 17:43:05] [Iter  343/1050] R1[192/300]   | LR: 0.012178 | E:  -46.808165 | E_var:     0.2552 | E_err:   0.007894
[2025-10-07 17:43:13] [Iter  344/1050] R1[193/300]   | LR: 0.012060 | E:  -46.802612 | E_var:     0.1675 | E_err:   0.006396
[2025-10-07 17:43:21] [Iter  345/1050] R1[194/300]   | LR: 0.011942 | E:  -46.808766 | E_var:     0.1669 | E_err:   0.006383
[2025-10-07 17:43:28] [Iter  346/1050] R1[195/300]   | LR: 0.011825 | E:  -46.806203 | E_var:     0.1508 | E_err:   0.006069
[2025-10-07 17:43:36] [Iter  347/1050] R1[196/300]   | LR: 0.011709 | E:  -46.810205 | E_var:     0.1403 | E_err:   0.005853
[2025-10-07 17:43:44] [Iter  348/1050] R1[197/300]   | LR: 0.011593 | E:  -46.807462 | E_var:     0.1802 | E_err:   0.006633
[2025-10-07 17:43:52] [Iter  349/1050] R1[198/300]   | LR: 0.011478 | E:  -46.811709 | E_var:     0.1697 | E_err:   0.006437
[2025-10-07 17:44:00] [Iter  350/1050] R1[199/300]   | LR: 0.011364 | E:  -46.805830 | E_var:     0.1834 | E_err:   0.006692
[2025-10-07 17:44:08] [Iter  351/1050] R1[200/300]   | LR: 0.011250 | E:  -46.795474 | E_var:     0.1856 | E_err:   0.006731
[2025-10-07 17:44:15] [Iter  352/1050] R1[201/300]   | LR: 0.011137 | E:  -46.820245 | E_var:     0.1548 | E_err:   0.006148
[2025-10-07 17:44:23] [Iter  353/1050] R1[202/300]   | LR: 0.011025 | E:  -46.809091 | E_var:     0.1462 | E_err:   0.005973
[2025-10-07 17:44:31] [Iter  354/1050] R1[203/300]   | LR: 0.010913 | E:  -46.813338 | E_var:     0.1689 | E_err:   0.006422
[2025-10-07 17:44:39] [Iter  355/1050] R1[204/300]   | LR: 0.010802 | E:  -46.817742 | E_var:     0.1459 | E_err:   0.005967
[2025-10-07 17:44:47] [Iter  356/1050] R1[205/300]   | LR: 0.010692 | E:  -46.819991 | E_var:     0.1458 | E_err:   0.005966
[2025-10-07 17:44:54] [Iter  357/1050] R1[206/300]   | LR: 0.010583 | E:  -46.803641 | E_var:     0.1842 | E_err:   0.006706
[2025-10-07 17:45:02] [Iter  358/1050] R1[207/300]   | LR: 0.010474 | E:  -46.812529 | E_var:     0.1778 | E_err:   0.006589
[2025-10-07 17:45:10] [Iter  359/1050] R1[208/300]   | LR: 0.010366 | E:  -46.806482 | E_var:     0.1564 | E_err:   0.006180
[2025-10-07 17:45:18] [Iter  360/1050] R1[209/300]   | LR: 0.010259 | E:  -46.800942 | E_var:     0.3087 | E_err:   0.008681
[2025-10-07 17:45:26] [Iter  361/1050] R1[210/300]   | LR: 0.010153 | E:  -46.805329 | E_var:     0.1640 | E_err:   0.006327
[2025-10-07 17:45:33] [Iter  362/1050] R1[211/300]   | LR: 0.010047 | E:  -46.806019 | E_var:     0.1733 | E_err:   0.006504
[2025-10-07 17:45:41] [Iter  363/1050] R1[212/300]   | LR: 0.009943 | E:  -46.817466 | E_var:     0.1692 | E_err:   0.006426
[2025-10-07 17:45:49] [Iter  364/1050] R1[213/300]   | LR: 0.009839 | E:  -46.825005 | E_var:     0.2644 | E_err:   0.008034
[2025-10-07 17:45:57] [Iter  365/1050] R1[214/300]   | LR: 0.009736 | E:  -46.825311 | E_var:     0.2604 | E_err:   0.007974
[2025-10-07 17:46:05] [Iter  366/1050] R1[215/300]   | LR: 0.009633 | E:  -46.816190 | E_var:     0.1459 | E_err:   0.005969
[2025-10-07 17:46:12] [Iter  367/1050] R1[216/300]   | LR: 0.009532 | E:  -46.805407 | E_var:     0.1604 | E_err:   0.006257
[2025-10-07 17:46:20] [Iter  368/1050] R1[217/300]   | LR: 0.009432 | E:  -46.816146 | E_var:     0.1931 | E_err:   0.006867
[2025-10-07 17:46:28] [Iter  369/1050] R1[218/300]   | LR: 0.009332 | E:  -46.804940 | E_var:     0.1578 | E_err:   0.006206
[2025-10-07 17:46:36] [Iter  370/1050] R1[219/300]   | LR: 0.009234 | E:  -46.814478 | E_var:     0.1628 | E_err:   0.006305
[2025-10-07 17:46:44] [Iter  371/1050] R1[220/300]   | LR: 0.009136 | E:  -46.798361 | E_var:     0.2123 | E_err:   0.007199
[2025-10-07 17:46:51] [Iter  372/1050] R1[221/300]   | LR: 0.009039 | E:  -46.805818 | E_var:     0.1433 | E_err:   0.005916
[2025-10-07 17:46:59] [Iter  373/1050] R1[222/300]   | LR: 0.008943 | E:  -46.807699 | E_var:     0.2417 | E_err:   0.007682
[2025-10-07 17:47:07] [Iter  374/1050] R1[223/300]   | LR: 0.008848 | E:  -46.815011 | E_var:     0.1821 | E_err:   0.006668
[2025-10-07 17:47:15] [Iter  375/1050] R1[224/300]   | LR: 0.008754 | E:  -46.818879 | E_var:     0.2575 | E_err:   0.007929
[2025-10-07 17:47:23] [Iter  376/1050] R1[225/300]   | LR: 0.008661 | E:  -46.809341 | E_var:     0.1930 | E_err:   0.006865
[2025-10-07 17:47:30] [Iter  377/1050] R1[226/300]   | LR: 0.008569 | E:  -46.810713 | E_var:     0.1907 | E_err:   0.006823
[2025-10-07 17:47:38] [Iter  378/1050] R1[227/300]   | LR: 0.008478 | E:  -46.816517 | E_var:     0.1636 | E_err:   0.006321
[2025-10-07 17:47:46] [Iter  379/1050] R1[228/300]   | LR: 0.008388 | E:  -46.815344 | E_var:     0.1766 | E_err:   0.006567
[2025-10-07 17:47:54] [Iter  380/1050] R1[229/300]   | LR: 0.008299 | E:  -46.803285 | E_var:     0.1977 | E_err:   0.006947
[2025-10-07 17:48:02] [Iter  381/1050] R1[230/300]   | LR: 0.008211 | E:  -46.817734 | E_var:     0.1616 | E_err:   0.006282
[2025-10-07 17:48:09] [Iter  382/1050] R1[231/300]   | LR: 0.008124 | E:  -46.807992 | E_var:     0.1912 | E_err:   0.006832
[2025-10-07 17:48:17] [Iter  383/1050] R1[232/300]   | LR: 0.008038 | E:  -46.809620 | E_var:     0.1590 | E_err:   0.006230
[2025-10-07 17:48:25] [Iter  384/1050] R1[233/300]   | LR: 0.007953 | E:  -46.820991 | E_var:     0.2615 | E_err:   0.007990
[2025-10-07 17:48:33] [Iter  385/1050] R1[234/300]   | LR: 0.007869 | E:  -46.810861 | E_var:     0.1965 | E_err:   0.006926
[2025-10-07 17:48:40] [Iter  386/1050] R1[235/300]   | LR: 0.007786 | E:  -46.799912 | E_var:     0.1521 | E_err:   0.006094
[2025-10-07 17:48:48] [Iter  387/1050] R1[236/300]   | LR: 0.007704 | E:  -46.803782 | E_var:     0.1450 | E_err:   0.005950
[2025-10-07 17:48:56] [Iter  388/1050] R1[237/300]   | LR: 0.007623 | E:  -46.802511 | E_var:     0.1539 | E_err:   0.006131
[2025-10-07 17:49:04] [Iter  389/1050] R1[238/300]   | LR: 0.007543 | E:  -46.799941 | E_var:     0.1657 | E_err:   0.006361
[2025-10-07 17:49:12] [Iter  390/1050] R1[239/300]   | LR: 0.007465 | E:  -46.804089 | E_var:     0.1797 | E_err:   0.006623
[2025-10-07 17:49:19] [Iter  391/1050] R1[240/300]   | LR: 0.007387 | E:  -46.804284 | E_var:     0.1696 | E_err:   0.006435
[2025-10-07 17:49:27] [Iter  392/1050] R1[241/300]   | LR: 0.007311 | E:  -46.810950 | E_var:     0.2982 | E_err:   0.008532
[2025-10-07 17:49:35] [Iter  393/1050] R1[242/300]   | LR: 0.007236 | E:  -46.811077 | E_var:     0.1515 | E_err:   0.006082
[2025-10-07 17:49:43] [Iter  394/1050] R1[243/300]   | LR: 0.007161 | E:  -46.798921 | E_var:     0.2604 | E_err:   0.007974
[2025-10-07 17:49:51] [Iter  395/1050] R1[244/300]   | LR: 0.007088 | E:  -46.814292 | E_var:     0.1960 | E_err:   0.006917
[2025-10-07 17:49:58] [Iter  396/1050] R1[245/300]   | LR: 0.007017 | E:  -46.801317 | E_var:     0.1800 | E_err:   0.006629
[2025-10-07 17:50:06] [Iter  397/1050] R1[246/300]   | LR: 0.006946 | E:  -46.802054 | E_var:     0.1332 | E_err:   0.005703
[2025-10-07 17:50:14] [Iter  398/1050] R1[247/300]   | LR: 0.006876 | E:  -46.797303 | E_var:     0.2520 | E_err:   0.007843
[2025-10-07 17:50:22] [Iter  399/1050] R1[248/300]   | LR: 0.006808 | E:  -46.801891 | E_var:     0.1790 | E_err:   0.006611
[2025-10-07 17:50:30] [Iter  400/1050] R1[249/300]   | LR: 0.006741 | E:  -46.812718 | E_var:     0.1642 | E_err:   0.006331
[2025-10-07 17:50:30] ✓ Checkpoint saved: checkpoint_iter_000400.pkl
[2025-10-07 17:50:37] [Iter  401/1050] R1[250/300]   | LR: 0.006675 | E:  -46.814601 | E_var:     0.1517 | E_err:   0.006085
[2025-10-07 17:50:45] [Iter  402/1050] R1[251/300]   | LR: 0.006610 | E:  -46.806787 | E_var:     0.1637 | E_err:   0.006322
[2025-10-07 17:50:53] [Iter  403/1050] R1[252/300]   | LR: 0.006546 | E:  -46.813847 | E_var:     0.1439 | E_err:   0.005927
[2025-10-07 17:51:01] [Iter  404/1050] R1[253/300]   | LR: 0.006484 | E:  -46.826250 | E_var:     0.2849 | E_err:   0.008340
[2025-10-07 17:51:09] [Iter  405/1050] R1[254/300]   | LR: 0.006422 | E:  -46.815519 | E_var:     0.1454 | E_err:   0.005958
[2025-10-07 17:51:16] [Iter  406/1050] R1[255/300]   | LR: 0.006362 | E:  -46.799947 | E_var:     0.2177 | E_err:   0.007290
[2025-10-07 17:51:24] [Iter  407/1050] R1[256/300]   | LR: 0.006304 | E:  -46.804779 | E_var:     0.1768 | E_err:   0.006570
[2025-10-07 17:51:32] [Iter  408/1050] R1[257/300]   | LR: 0.006246 | E:  -46.819839 | E_var:     0.1860 | E_err:   0.006738
[2025-10-07 17:51:40] [Iter  409/1050] R1[258/300]   | LR: 0.006190 | E:  -46.818871 | E_var:     0.1612 | E_err:   0.006274
[2025-10-07 17:51:48] [Iter  410/1050] R1[259/300]   | LR: 0.006135 | E:  -46.816514 | E_var:     0.1710 | E_err:   0.006462
[2025-10-07 17:51:55] [Iter  411/1050] R1[260/300]   | LR: 0.006081 | E:  -46.817525 | E_var:     0.1673 | E_err:   0.006391
[2025-10-07 17:52:03] [Iter  412/1050] R1[261/300]   | LR: 0.006028 | E:  -46.807314 | E_var:     0.1756 | E_err:   0.006548
[2025-10-07 17:52:11] [Iter  413/1050] R1[262/300]   | LR: 0.005977 | E:  -46.818195 | E_var:     0.1540 | E_err:   0.006132
[2025-10-07 17:52:19] [Iter  414/1050] R1[263/300]   | LR: 0.005927 | E:  -46.820832 | E_var:     0.1395 | E_err:   0.005835
[2025-10-07 17:52:27] [Iter  415/1050] R1[264/300]   | LR: 0.005878 | E:  -46.800250 | E_var:     0.1674 | E_err:   0.006394
[2025-10-07 17:52:34] [Iter  416/1050] R1[265/300]   | LR: 0.005830 | E:  -46.814699 | E_var:     0.2265 | E_err:   0.007436
[2025-10-07 17:52:42] [Iter  417/1050] R1[266/300]   | LR: 0.005784 | E:  -46.807080 | E_var:     0.3269 | E_err:   0.008934
[2025-10-07 17:52:50] [Iter  418/1050] R1[267/300]   | LR: 0.005739 | E:  -46.808419 | E_var:     0.2006 | E_err:   0.006998
[2025-10-07 17:52:58] [Iter  419/1050] R1[268/300]   | LR: 0.005695 | E:  -46.802533 | E_var:     0.2166 | E_err:   0.007272
[2025-10-07 17:53:05] [Iter  420/1050] R1[269/300]   | LR: 0.005653 | E:  -46.812761 | E_var:     0.1655 | E_err:   0.006357
[2025-10-07 17:53:13] [Iter  421/1050] R1[270/300]   | LR: 0.005612 | E:  -46.815688 | E_var:     0.2157 | E_err:   0.007257
[2025-10-07 17:53:21] [Iter  422/1050] R1[271/300]   | LR: 0.005572 | E:  -46.815065 | E_var:     0.2144 | E_err:   0.007234
[2025-10-07 17:53:29] [Iter  423/1050] R1[272/300]   | LR: 0.005534 | E:  -46.815269 | E_var:     0.1659 | E_err:   0.006365
[2025-10-07 17:53:37] [Iter  424/1050] R1[273/300]   | LR: 0.005496 | E:  -46.815286 | E_var:     0.1687 | E_err:   0.006418
[2025-10-07 17:53:44] [Iter  425/1050] R1[274/300]   | LR: 0.005460 | E:  -46.804034 | E_var:     0.2176 | E_err:   0.007289
[2025-10-07 17:53:52] [Iter  426/1050] R1[275/300]   | LR: 0.005426 | E:  -46.804487 | E_var:     0.1635 | E_err:   0.006318
[2025-10-07 17:54:00] [Iter  427/1050] R1[276/300]   | LR: 0.005393 | E:  -46.807969 | E_var:     0.2923 | E_err:   0.008448
[2025-10-07 17:54:08] [Iter  428/1050] R1[277/300]   | LR: 0.005361 | E:  -46.801215 | E_var:     0.2133 | E_err:   0.007216
[2025-10-07 17:54:16] [Iter  429/1050] R1[278/300]   | LR: 0.005330 | E:  -46.816435 | E_var:     0.1734 | E_err:   0.006506
[2025-10-07 17:54:23] [Iter  430/1050] R1[279/300]   | LR: 0.005301 | E:  -46.813512 | E_var:     0.1790 | E_err:   0.006610
[2025-10-07 17:54:31] [Iter  431/1050] R1[280/300]   | LR: 0.005273 | E:  -46.801465 | E_var:     0.2350 | E_err:   0.007574
[2025-10-07 17:54:39] [Iter  432/1050] R1[281/300]   | LR: 0.005247 | E:  -46.805653 | E_var:     0.1428 | E_err:   0.005905
[2025-10-07 17:54:47] [Iter  433/1050] R1[282/300]   | LR: 0.005221 | E:  -46.803650 | E_var:     0.1986 | E_err:   0.006963
[2025-10-07 17:54:55] [Iter  434/1050] R1[283/300]   | LR: 0.005198 | E:  -46.809568 | E_var:     0.2541 | E_err:   0.007876
[2025-10-07 17:55:02] [Iter  435/1050] R1[284/300]   | LR: 0.005175 | E:  -46.808382 | E_var:     0.1594 | E_err:   0.006238
[2025-10-07 17:55:10] [Iter  436/1050] R1[285/300]   | LR: 0.005154 | E:  -46.803858 | E_var:     0.1716 | E_err:   0.006473
[2025-10-07 17:55:18] [Iter  437/1050] R1[286/300]   | LR: 0.005134 | E:  -46.806938 | E_var:     0.1693 | E_err:   0.006429
[2025-10-07 17:55:26] [Iter  438/1050] R1[287/300]   | LR: 0.005116 | E:  -46.811312 | E_var:     0.2019 | E_err:   0.007022
[2025-10-07 17:55:33] [Iter  439/1050] R1[288/300]   | LR: 0.005099 | E:  -46.823342 | E_var:     0.1892 | E_err:   0.006796
[2025-10-07 17:55:41] [Iter  440/1050] R1[289/300]   | LR: 0.005083 | E:  -46.809256 | E_var:     0.1568 | E_err:   0.006188
[2025-10-07 17:55:49] [Iter  441/1050] R1[290/300]   | LR: 0.005068 | E:  -46.812943 | E_var:     0.2000 | E_err:   0.006988
[2025-10-07 17:55:57] [Iter  442/1050] R1[291/300]   | LR: 0.005055 | E:  -46.800156 | E_var:     0.2052 | E_err:   0.007078
[2025-10-07 17:56:05] [Iter  443/1050] R1[292/300]   | LR: 0.005044 | E:  -46.805470 | E_var:     0.2864 | E_err:   0.008362
[2025-10-07 17:56:12] [Iter  444/1050] R1[293/300]   | LR: 0.005034 | E:  -46.809666 | E_var:     0.2942 | E_err:   0.008475
[2025-10-07 17:56:20] [Iter  445/1050] R1[294/300]   | LR: 0.005025 | E:  -46.805772 | E_var:     0.1728 | E_err:   0.006495
[2025-10-07 17:56:28] [Iter  446/1050] R1[295/300]   | LR: 0.005017 | E:  -46.821224 | E_var:     0.1912 | E_err:   0.006832
[2025-10-07 17:56:36] [Iter  447/1050] R1[296/300]   | LR: 0.005011 | E:  -46.805318 | E_var:     0.1850 | E_err:   0.006721
[2025-10-07 17:56:44] [Iter  448/1050] R1[297/300]   | LR: 0.005006 | E:  -46.807659 | E_var:     0.1657 | E_err:   0.006361
[2025-10-07 17:56:51] [Iter  449/1050] R1[298/300]   | LR: 0.005003 | E:  -46.805221 | E_var:     0.1941 | E_err:   0.006883
[2025-10-07 17:56:59] [Iter  450/1050] R1[299/300]   | LR: 0.005001 | E:  -46.821960 | E_var:     0.1458 | E_err:   0.005966
[2025-10-07 17:56:59] 🔄 RESTART #2 | Period: 600
[2025-10-07 17:57:07] [Iter  451/1050] R2[0/600]     | LR: 0.030000 | E:  -46.814135 | E_var:     0.1860 | E_err:   0.006738
[2025-10-07 17:57:15] [Iter  452/1050] R2[1/600]     | LR: 0.030000 | E:  -46.809013 | E_var:     0.2834 | E_err:   0.008317
[2025-10-07 17:57:23] [Iter  453/1050] R2[2/600]     | LR: 0.029999 | E:  -46.818037 | E_var:     0.1772 | E_err:   0.006578
[2025-10-07 17:57:30] [Iter  454/1050] R2[3/600]     | LR: 0.029998 | E:  -46.813100 | E_var:     0.1534 | E_err:   0.006120
[2025-10-07 17:57:38] [Iter  455/1050] R2[4/600]     | LR: 0.029997 | E:  -46.810660 | E_var:     0.2206 | E_err:   0.007339
[2025-10-07 17:57:46] [Iter  456/1050] R2[5/600]     | LR: 0.029996 | E:  -46.807651 | E_var:     0.2288 | E_err:   0.007475
[2025-10-07 17:57:54] [Iter  457/1050] R2[6/600]     | LR: 0.029994 | E:  -46.826793 | E_var:     0.1936 | E_err:   0.006875
[2025-10-07 17:58:01] [Iter  458/1050] R2[7/600]     | LR: 0.029992 | E:  -46.829781 | E_var:     0.2337 | E_err:   0.007554
[2025-10-07 17:58:09] [Iter  459/1050] R2[8/600]     | LR: 0.029989 | E:  -46.815357 | E_var:     0.1369 | E_err:   0.005782
[2025-10-07 17:58:17] [Iter  460/1050] R2[9/600]     | LR: 0.029986 | E:  -46.806777 | E_var:     0.1484 | E_err:   0.006019
[2025-10-07 17:58:25] [Iter  461/1050] R2[10/600]    | LR: 0.029983 | E:  -46.821338 | E_var:     0.1642 | E_err:   0.006332
[2025-10-07 17:58:33] [Iter  462/1050] R2[11/600]    | LR: 0.029979 | E:  -46.813378 | E_var:     0.2036 | E_err:   0.007050
[2025-10-07 17:58:40] [Iter  463/1050] R2[12/600]    | LR: 0.029975 | E:  -46.809670 | E_var:     0.1795 | E_err:   0.006620
[2025-10-07 17:58:48] [Iter  464/1050] R2[13/600]    | LR: 0.029971 | E:  -46.811742 | E_var:     0.3891 | E_err:   0.009747
[2025-10-07 17:58:56] [Iter  465/1050] R2[14/600]    | LR: 0.029966 | E:  -46.814492 | E_var:     0.1934 | E_err:   0.006872
[2025-10-07 17:59:04] [Iter  466/1050] R2[15/600]    | LR: 0.029961 | E:  -46.810908 | E_var:     0.2527 | E_err:   0.007855
[2025-10-07 17:59:12] [Iter  467/1050] R2[16/600]    | LR: 0.029956 | E:  -46.815967 | E_var:     0.1605 | E_err:   0.006260
[2025-10-07 17:59:19] [Iter  468/1050] R2[17/600]    | LR: 0.029951 | E:  -46.807459 | E_var:     0.1761 | E_err:   0.006557
[2025-10-07 17:59:27] [Iter  469/1050] R2[18/600]    | LR: 0.029945 | E:  -46.805141 | E_var:     0.1812 | E_err:   0.006651
[2025-10-07 17:59:35] [Iter  470/1050] R2[19/600]    | LR: 0.029938 | E:  -46.815167 | E_var:     0.1410 | E_err:   0.005867
[2025-10-07 17:59:43] [Iter  471/1050] R2[20/600]    | LR: 0.029932 | E:  -46.799633 | E_var:     0.1826 | E_err:   0.006677
[2025-10-07 17:59:51] [Iter  472/1050] R2[21/600]    | LR: 0.029925 | E:  -46.810100 | E_var:     0.2910 | E_err:   0.008429
[2025-10-07 17:59:58] [Iter  473/1050] R2[22/600]    | LR: 0.029917 | E:  -46.802914 | E_var:     0.2126 | E_err:   0.007205
[2025-10-07 18:00:06] [Iter  474/1050] R2[23/600]    | LR: 0.029909 | E:  -46.812153 | E_var:     0.1978 | E_err:   0.006949
[2025-10-07 18:00:14] [Iter  475/1050] R2[24/600]    | LR: 0.029901 | E:  -46.813250 | E_var:     0.1987 | E_err:   0.006965
[2025-10-07 18:00:22] [Iter  476/1050] R2[25/600]    | LR: 0.029893 | E:  -46.800165 | E_var:     0.1842 | E_err:   0.006706
[2025-10-07 18:00:30] [Iter  477/1050] R2[26/600]    | LR: 0.029884 | E:  -46.807758 | E_var:     0.2034 | E_err:   0.007047
[2025-10-07 18:00:37] [Iter  478/1050] R2[27/600]    | LR: 0.029875 | E:  -46.811378 | E_var:     0.2296 | E_err:   0.007486
[2025-10-07 18:00:45] [Iter  479/1050] R2[28/600]    | LR: 0.029866 | E:  -46.824728 | E_var:     0.1902 | E_err:   0.006815
[2025-10-07 18:00:53] [Iter  480/1050] R2[29/600]    | LR: 0.029856 | E:  -46.821897 | E_var:     0.1513 | E_err:   0.006078
[2025-10-07 18:01:01] [Iter  481/1050] R2[30/600]    | LR: 0.029846 | E:  -46.809983 | E_var:     0.1280 | E_err:   0.005589
[2025-10-07 18:01:08] [Iter  482/1050] R2[31/600]    | LR: 0.029836 | E:  -46.817632 | E_var:     0.2125 | E_err:   0.007202
[2025-10-07 18:01:16] [Iter  483/1050] R2[32/600]    | LR: 0.029825 | E:  -46.802239 | E_var:     0.2675 | E_err:   0.008081
[2025-10-07 18:01:24] [Iter  484/1050] R2[33/600]    | LR: 0.029814 | E:  -46.813602 | E_var:     0.1729 | E_err:   0.006497
[2025-10-07 18:01:32] [Iter  485/1050] R2[34/600]    | LR: 0.029802 | E:  -46.806247 | E_var:     0.1901 | E_err:   0.006812
[2025-10-07 18:01:40] [Iter  486/1050] R2[35/600]    | LR: 0.029791 | E:  -46.806317 | E_var:     0.1808 | E_err:   0.006643
[2025-10-07 18:01:47] [Iter  487/1050] R2[36/600]    | LR: 0.029779 | E:  -46.814286 | E_var:     0.1916 | E_err:   0.006839
[2025-10-07 18:01:55] [Iter  488/1050] R2[37/600]    | LR: 0.029766 | E:  -46.805752 | E_var:     0.1420 | E_err:   0.005887
[2025-10-07 18:02:03] [Iter  489/1050] R2[38/600]    | LR: 0.029753 | E:  -46.808567 | E_var:     0.2071 | E_err:   0.007110
[2025-10-07 18:02:11] [Iter  490/1050] R2[39/600]    | LR: 0.029740 | E:  -46.805370 | E_var:     0.1974 | E_err:   0.006942
[2025-10-07 18:02:19] [Iter  491/1050] R2[40/600]    | LR: 0.029727 | E:  -46.811815 | E_var:     0.1447 | E_err:   0.005944
[2025-10-07 18:02:26] [Iter  492/1050] R2[41/600]    | LR: 0.029713 | E:  -46.807860 | E_var:     0.3194 | E_err:   0.008830
[2025-10-07 18:02:34] [Iter  493/1050] R2[42/600]    | LR: 0.029699 | E:  -46.821462 | E_var:     0.1375 | E_err:   0.005793
[2025-10-07 18:02:42] [Iter  494/1050] R2[43/600]    | LR: 0.029685 | E:  -46.807985 | E_var:     0.1706 | E_err:   0.006455
[2025-10-07 18:02:50] [Iter  495/1050] R2[44/600]    | LR: 0.029670 | E:  -46.804396 | E_var:     0.1973 | E_err:   0.006940
[2025-10-07 18:02:58] [Iter  496/1050] R2[45/600]    | LR: 0.029655 | E:  -46.812505 | E_var:     0.1617 | E_err:   0.006282
[2025-10-07 18:03:05] [Iter  497/1050] R2[46/600]    | LR: 0.029639 | E:  -46.809685 | E_var:     0.2021 | E_err:   0.007024
[2025-10-07 18:03:13] [Iter  498/1050] R2[47/600]    | LR: 0.029623 | E:  -46.811202 | E_var:     0.3012 | E_err:   0.008576
[2025-10-07 18:03:21] [Iter  499/1050] R2[48/600]    | LR: 0.029607 | E:  -46.816929 | E_var:     0.2000 | E_err:   0.006988
[2025-10-07 18:03:29] [Iter  500/1050] R2[49/600]    | LR: 0.029591 | E:  -46.807671 | E_var:     0.1590 | E_err:   0.006230
[2025-10-07 18:03:29] ✓ Checkpoint saved: checkpoint_iter_000500.pkl
[2025-10-07 18:03:36] [Iter  501/1050] R2[50/600]    | LR: 0.029574 | E:  -46.813323 | E_var:     0.1742 | E_err:   0.006522
[2025-10-07 18:03:44] [Iter  502/1050] R2[51/600]    | LR: 0.029557 | E:  -46.817245 | E_var:     0.1816 | E_err:   0.006659
[2025-10-07 18:03:52] [Iter  503/1050] R2[52/600]    | LR: 0.029540 | E:  -46.797481 | E_var:     0.1729 | E_err:   0.006497
[2025-10-07 18:04:00] [Iter  504/1050] R2[53/600]    | LR: 0.029522 | E:  -46.814603 | E_var:     0.2226 | E_err:   0.007373
[2025-10-07 18:04:08] [Iter  505/1050] R2[54/600]    | LR: 0.029504 | E:  -46.815361 | E_var:     0.1923 | E_err:   0.006852
[2025-10-07 18:04:15] [Iter  506/1050] R2[55/600]    | LR: 0.029485 | E:  -46.814963 | E_var:     0.1539 | E_err:   0.006130
[2025-10-07 18:04:23] [Iter  507/1050] R2[56/600]    | LR: 0.029466 | E:  -46.803203 | E_var:     0.1885 | E_err:   0.006783
[2025-10-07 18:04:31] [Iter  508/1050] R2[57/600]    | LR: 0.029447 | E:  -46.806157 | E_var:     0.1704 | E_err:   0.006450
[2025-10-07 18:04:39] [Iter  509/1050] R2[58/600]    | LR: 0.029428 | E:  -46.813197 | E_var:     0.2155 | E_err:   0.007253
[2025-10-07 18:04:47] [Iter  510/1050] R2[59/600]    | LR: 0.029408 | E:  -46.804948 | E_var:     0.1574 | E_err:   0.006199
[2025-10-07 18:04:54] [Iter  511/1050] R2[60/600]    | LR: 0.029388 | E:  -46.810853 | E_var:     0.1430 | E_err:   0.005908
[2025-10-07 18:05:02] [Iter  512/1050] R2[61/600]    | LR: 0.029368 | E:  -46.812573 | E_var:     0.1416 | E_err:   0.005879
[2025-10-07 18:05:10] [Iter  513/1050] R2[62/600]    | LR: 0.029347 | E:  -46.820892 | E_var:     0.1857 | E_err:   0.006734
[2025-10-07 18:05:18] [Iter  514/1050] R2[63/600]    | LR: 0.029326 | E:  -46.803781 | E_var:     0.1700 | E_err:   0.006443
[2025-10-07 18:05:26] [Iter  515/1050] R2[64/600]    | LR: 0.029305 | E:  -46.802962 | E_var:     0.1257 | E_err:   0.005539
[2025-10-07 18:05:33] [Iter  516/1050] R2[65/600]    | LR: 0.029283 | E:  -46.808629 | E_var:     0.2017 | E_err:   0.007017
[2025-10-07 18:05:41] [Iter  517/1050] R2[66/600]    | LR: 0.029261 | E:  -46.806220 | E_var:     0.1840 | E_err:   0.006702
[2025-10-07 18:05:49] [Iter  518/1050] R2[67/600]    | LR: 0.029239 | E:  -46.803070 | E_var:     0.1740 | E_err:   0.006517
[2025-10-07 18:05:57] [Iter  519/1050] R2[68/600]    | LR: 0.029216 | E:  -46.799547 | E_var:     0.1811 | E_err:   0.006648
[2025-10-07 18:06:05] [Iter  520/1050] R2[69/600]    | LR: 0.029193 | E:  -46.805646 | E_var:     0.1680 | E_err:   0.006404
[2025-10-07 18:06:13] [Iter  521/1050] R2[70/600]    | LR: 0.029170 | E:  -46.798938 | E_var:     0.1614 | E_err:   0.006278
[2025-10-07 18:06:20] [Iter  522/1050] R2[71/600]    | LR: 0.029146 | E:  -46.798200 | E_var:     0.2480 | E_err:   0.007782
[2025-10-07 18:06:28] [Iter  523/1050] R2[72/600]    | LR: 0.029122 | E:  -46.813234 | E_var:     0.1789 | E_err:   0.006608
[2025-10-07 18:06:36] [Iter  524/1050] R2[73/600]    | LR: 0.029098 | E:  -46.807121 | E_var:     0.1588 | E_err:   0.006227
[2025-10-07 18:06:44] [Iter  525/1050] R2[74/600]    | LR: 0.029073 | E:  -46.809206 | E_var:     0.1863 | E_err:   0.006744
[2025-10-07 18:06:52] [Iter  526/1050] R2[75/600]    | LR: 0.029048 | E:  -46.819080 | E_var:     0.1550 | E_err:   0.006151
[2025-10-07 18:07:00] [Iter  527/1050] R2[76/600]    | LR: 0.029023 | E:  -46.820153 | E_var:     0.2166 | E_err:   0.007272
[2025-10-07 18:07:08] [Iter  528/1050] R2[77/600]    | LR: 0.028998 | E:  -46.806337 | E_var:     0.1908 | E_err:   0.006825
[2025-10-07 18:07:16] [Iter  529/1050] R2[78/600]    | LR: 0.028972 | E:  -46.811165 | E_var:     0.1673 | E_err:   0.006390
[2025-10-07 18:07:24] [Iter  530/1050] R2[79/600]    | LR: 0.028946 | E:  -46.819375 | E_var:     0.1730 | E_err:   0.006499
[2025-10-07 18:07:32] [Iter  531/1050] R2[80/600]    | LR: 0.028919 | E:  -46.807172 | E_var:     0.1460 | E_err:   0.005970
[2025-10-07 18:07:39] [Iter  532/1050] R2[81/600]    | LR: 0.028893 | E:  -46.829097 | E_var:     0.1922 | E_err:   0.006851
[2025-10-07 18:07:47] [Iter  533/1050] R2[82/600]    | LR: 0.028865 | E:  -46.815426 | E_var:     0.1366 | E_err:   0.005776
[2025-10-07 18:07:55] [Iter  534/1050] R2[83/600]    | LR: 0.028838 | E:  -46.804931 | E_var:     0.2053 | E_err:   0.007080
[2025-10-07 18:08:03] [Iter  535/1050] R2[84/600]    | LR: 0.028810 | E:  -46.803521 | E_var:     0.2051 | E_err:   0.007077
[2025-10-07 18:08:11] [Iter  536/1050] R2[85/600]    | LR: 0.028782 | E:  -46.814082 | E_var:     0.1742 | E_err:   0.006521
[2025-10-07 18:08:18] [Iter  537/1050] R2[86/600]    | LR: 0.028754 | E:  -46.816231 | E_var:     0.1807 | E_err:   0.006643
[2025-10-07 18:08:26] [Iter  538/1050] R2[87/600]    | LR: 0.028725 | E:  -46.813329 | E_var:     0.1831 | E_err:   0.006686
[2025-10-07 18:08:34] [Iter  539/1050] R2[88/600]    | LR: 0.028696 | E:  -46.808574 | E_var:     0.2484 | E_err:   0.007788
[2025-10-07 18:08:42] [Iter  540/1050] R2[89/600]    | LR: 0.028667 | E:  -46.811607 | E_var:     0.1737 | E_err:   0.006512
[2025-10-07 18:08:50] [Iter  541/1050] R2[90/600]    | LR: 0.028638 | E:  -46.815791 | E_var:     0.1913 | E_err:   0.006834
[2025-10-07 18:08:58] [Iter  542/1050] R2[91/600]    | LR: 0.028608 | E:  -46.814010 | E_var:     0.2254 | E_err:   0.007419
[2025-10-07 18:09:05] [Iter  543/1050] R2[92/600]    | LR: 0.028578 | E:  -46.802173 | E_var:     0.1727 | E_err:   0.006493
[2025-10-07 18:09:13] [Iter  544/1050] R2[93/600]    | LR: 0.028547 | E:  -46.811789 | E_var:     0.1713 | E_err:   0.006466
[2025-10-07 18:09:21] [Iter  545/1050] R2[94/600]    | LR: 0.028516 | E:  -46.813981 | E_var:     0.1917 | E_err:   0.006842
[2025-10-07 18:09:29] [Iter  546/1050] R2[95/600]    | LR: 0.028485 | E:  -46.822012 | E_var:     0.1347 | E_err:   0.005734
[2025-10-07 18:09:37] [Iter  547/1050] R2[96/600]    | LR: 0.028454 | E:  -46.810185 | E_var:     0.1551 | E_err:   0.006155
[2025-10-07 18:09:44] [Iter  548/1050] R2[97/600]    | LR: 0.028422 | E:  -46.816918 | E_var:     0.1924 | E_err:   0.006854
[2025-10-07 18:09:52] [Iter  549/1050] R2[98/600]    | LR: 0.028390 | E:  -46.821211 | E_var:     0.1611 | E_err:   0.006271
[2025-10-07 18:10:00] [Iter  550/1050] R2[99/600]    | LR: 0.028358 | E:  -46.815851 | E_var:     0.1713 | E_err:   0.006467
[2025-10-07 18:10:08] [Iter  551/1050] R2[100/600]   | LR: 0.028325 | E:  -46.803360 | E_var:     0.1430 | E_err:   0.005908
[2025-10-07 18:10:16] [Iter  552/1050] R2[101/600]   | LR: 0.028292 | E:  -46.810938 | E_var:     0.1894 | E_err:   0.006799
[2025-10-07 18:10:24] [Iter  553/1050] R2[102/600]   | LR: 0.028259 | E:  -46.810574 | E_var:     0.1587 | E_err:   0.006225
[2025-10-07 18:10:31] [Iter  554/1050] R2[103/600]   | LR: 0.028226 | E:  -46.812494 | E_var:     0.1935 | E_err:   0.006874
[2025-10-07 18:10:39] [Iter  555/1050] R2[104/600]   | LR: 0.028192 | E:  -46.809780 | E_var:     0.1649 | E_err:   0.006346
[2025-10-07 18:10:47] [Iter  556/1050] R2[105/600]   | LR: 0.028158 | E:  -46.810275 | E_var:     0.2253 | E_err:   0.007417
[2025-10-07 18:10:55] [Iter  557/1050] R2[106/600]   | LR: 0.028124 | E:  -46.808274 | E_var:     0.1804 | E_err:   0.006636
[2025-10-07 18:11:03] [Iter  558/1050] R2[107/600]   | LR: 0.028089 | E:  -46.819792 | E_var:     0.1367 | E_err:   0.005778
[2025-10-07 18:11:10] [Iter  559/1050] R2[108/600]   | LR: 0.028054 | E:  -46.805489 | E_var:     0.1836 | E_err:   0.006696
[2025-10-07 18:11:18] [Iter  560/1050] R2[109/600]   | LR: 0.028019 | E:  -46.822243 | E_var:     0.2147 | E_err:   0.007240
[2025-10-07 18:11:26] [Iter  561/1050] R2[110/600]   | LR: 0.027983 | E:  -46.810753 | E_var:     0.2219 | E_err:   0.007360
[2025-10-07 18:11:34] [Iter  562/1050] R2[111/600]   | LR: 0.027948 | E:  -46.808378 | E_var:     0.1949 | E_err:   0.006898
[2025-10-07 18:11:42] [Iter  563/1050] R2[112/600]   | LR: 0.027912 | E:  -46.803731 | E_var:     0.1754 | E_err:   0.006545
[2025-10-07 18:11:49] [Iter  564/1050] R2[113/600]   | LR: 0.027875 | E:  -46.808178 | E_var:     0.1503 | E_err:   0.006057
[2025-10-07 18:11:57] [Iter  565/1050] R2[114/600]   | LR: 0.027839 | E:  -46.810590 | E_var:     0.1847 | E_err:   0.006716
[2025-10-07 18:12:05] [Iter  566/1050] R2[115/600]   | LR: 0.027802 | E:  -46.809530 | E_var:     0.1614 | E_err:   0.006277
[2025-10-07 18:12:13] [Iter  567/1050] R2[116/600]   | LR: 0.027764 | E:  -46.815501 | E_var:     0.1395 | E_err:   0.005835
[2025-10-07 18:12:21] [Iter  568/1050] R2[117/600]   | LR: 0.027727 | E:  -46.825183 | E_var:     0.1626 | E_err:   0.006300
[2025-10-07 18:12:28] [Iter  569/1050] R2[118/600]   | LR: 0.027689 | E:  -46.804959 | E_var:     0.2581 | E_err:   0.007937
[2025-10-07 18:12:36] [Iter  570/1050] R2[119/600]   | LR: 0.027651 | E:  -46.804916 | E_var:     0.1500 | E_err:   0.006051
[2025-10-07 18:12:44] [Iter  571/1050] R2[120/600]   | LR: 0.027613 | E:  -46.808377 | E_var:     0.1611 | E_err:   0.006272
[2025-10-07 18:12:52] [Iter  572/1050] R2[121/600]   | LR: 0.027574 | E:  -46.802478 | E_var:     0.2770 | E_err:   0.008223
[2025-10-07 18:13:00] [Iter  573/1050] R2[122/600]   | LR: 0.027535 | E:  -46.813500 | E_var:     0.1899 | E_err:   0.006809
[2025-10-07 18:13:07] [Iter  574/1050] R2[123/600]   | LR: 0.027496 | E:  -46.812223 | E_var:     0.1420 | E_err:   0.005888
[2025-10-07 18:13:15] [Iter  575/1050] R2[124/600]   | LR: 0.027457 | E:  -46.812667 | E_var:     0.1701 | E_err:   0.006445
[2025-10-07 18:13:23] [Iter  576/1050] R2[125/600]   | LR: 0.027417 | E:  -46.816176 | E_var:     0.1798 | E_err:   0.006626
[2025-10-07 18:13:31] [Iter  577/1050] R2[126/600]   | LR: 0.027377 | E:  -46.805886 | E_var:     0.1662 | E_err:   0.006370
[2025-10-07 18:13:39] [Iter  578/1050] R2[127/600]   | LR: 0.027337 | E:  -46.820146 | E_var:     0.1415 | E_err:   0.005878
[2025-10-07 18:13:46] [Iter  579/1050] R2[128/600]   | LR: 0.027296 | E:  -46.809420 | E_var:     0.2098 | E_err:   0.007157
[2025-10-07 18:13:54] [Iter  580/1050] R2[129/600]   | LR: 0.027255 | E:  -46.805385 | E_var:     0.1783 | E_err:   0.006598
[2025-10-07 18:14:02] [Iter  581/1050] R2[130/600]   | LR: 0.027214 | E:  -46.806121 | E_var:     0.1754 | E_err:   0.006545
[2025-10-07 18:14:10] [Iter  582/1050] R2[131/600]   | LR: 0.027173 | E:  -46.819174 | E_var:     0.1767 | E_err:   0.006567
[2025-10-07 18:14:18] [Iter  583/1050] R2[132/600]   | LR: 0.027131 | E:  -46.812060 | E_var:     0.1480 | E_err:   0.006010
[2025-10-07 18:14:25] [Iter  584/1050] R2[133/600]   | LR: 0.027090 | E:  -46.817061 | E_var:     0.1689 | E_err:   0.006422
[2025-10-07 18:14:33] [Iter  585/1050] R2[134/600]   | LR: 0.027047 | E:  -46.805788 | E_var:     0.1853 | E_err:   0.006726
[2025-10-07 18:14:41] [Iter  586/1050] R2[135/600]   | LR: 0.027005 | E:  -46.799296 | E_var:     0.2219 | E_err:   0.007360
[2025-10-07 18:14:49] [Iter  587/1050] R2[136/600]   | LR: 0.026962 | E:  -46.815697 | E_var:     0.1693 | E_err:   0.006429
[2025-10-07 18:14:57] [Iter  588/1050] R2[137/600]   | LR: 0.026920 | E:  -46.809332 | E_var:     0.1553 | E_err:   0.006158
[2025-10-07 18:15:04] [Iter  589/1050] R2[138/600]   | LR: 0.026876 | E:  -46.809724 | E_var:     0.2670 | E_err:   0.008073
[2025-10-07 18:15:12] [Iter  590/1050] R2[139/600]   | LR: 0.026833 | E:  -46.811072 | E_var:     0.1593 | E_err:   0.006237
[2025-10-07 18:15:20] [Iter  591/1050] R2[140/600]   | LR: 0.026789 | E:  -46.809842 | E_var:     0.1478 | E_err:   0.006006
[2025-10-07 18:15:28] [Iter  592/1050] R2[141/600]   | LR: 0.026745 | E:  -46.812697 | E_var:     0.1923 | E_err:   0.006852
[2025-10-07 18:15:35] [Iter  593/1050] R2[142/600]   | LR: 0.026701 | E:  -46.822680 | E_var:     0.2388 | E_err:   0.007636
[2025-10-07 18:15:43] [Iter  594/1050] R2[143/600]   | LR: 0.026657 | E:  -46.815550 | E_var:     0.1693 | E_err:   0.006428
[2025-10-07 18:15:51] [Iter  595/1050] R2[144/600]   | LR: 0.026612 | E:  -46.802366 | E_var:     0.1542 | E_err:   0.006135
[2025-10-07 18:15:59] [Iter  596/1050] R2[145/600]   | LR: 0.026567 | E:  -46.813202 | E_var:     0.1568 | E_err:   0.006186
[2025-10-07 18:16:07] [Iter  597/1050] R2[146/600]   | LR: 0.026522 | E:  -46.826834 | E_var:     0.1987 | E_err:   0.006966
[2025-10-07 18:16:14] [Iter  598/1050] R2[147/600]   | LR: 0.026477 | E:  -46.822075 | E_var:     0.1950 | E_err:   0.006900
[2025-10-07 18:16:22] [Iter  599/1050] R2[148/600]   | LR: 0.026431 | E:  -46.812771 | E_var:     0.1970 | E_err:   0.006935
[2025-10-07 18:16:30] [Iter  600/1050] R2[149/600]   | LR: 0.026385 | E:  -46.814074 | E_var:     0.1882 | E_err:   0.006778
[2025-10-07 18:16:30] ✓ Checkpoint saved: checkpoint_iter_000600.pkl
[2025-10-07 18:16:38] [Iter  601/1050] R2[150/600]   | LR: 0.026339 | E:  -46.811975 | E_var:     0.1552 | E_err:   0.006155
[2025-10-07 18:16:46] [Iter  602/1050] R2[151/600]   | LR: 0.026292 | E:  -46.812435 | E_var:     0.1777 | E_err:   0.006586
[2025-10-07 18:16:53] [Iter  603/1050] R2[152/600]   | LR: 0.026246 | E:  -46.820585 | E_var:     0.2169 | E_err:   0.007277
[2025-10-07 18:17:01] [Iter  604/1050] R2[153/600]   | LR: 0.026199 | E:  -46.801770 | E_var:     0.1668 | E_err:   0.006381
[2025-10-07 18:17:09] [Iter  605/1050] R2[154/600]   | LR: 0.026152 | E:  -46.808842 | E_var:     0.1220 | E_err:   0.005457
[2025-10-07 18:17:17] [Iter  606/1050] R2[155/600]   | LR: 0.026104 | E:  -46.807134 | E_var:     0.1270 | E_err:   0.005567
[2025-10-07 18:17:25] [Iter  607/1050] R2[156/600]   | LR: 0.026057 | E:  -46.822877 | E_var:     0.1524 | E_err:   0.006100
[2025-10-07 18:17:32] [Iter  608/1050] R2[157/600]   | LR: 0.026009 | E:  -46.803802 | E_var:     0.1685 | E_err:   0.006414
[2025-10-07 18:17:40] [Iter  609/1050] R2[158/600]   | LR: 0.025961 | E:  -46.816564 | E_var:     0.1436 | E_err:   0.005920
[2025-10-07 18:17:48] [Iter  610/1050] R2[159/600]   | LR: 0.025913 | E:  -46.817714 | E_var:     0.3752 | E_err:   0.009571
[2025-10-07 18:17:56] [Iter  611/1050] R2[160/600]   | LR: 0.025864 | E:  -46.800656 | E_var:     0.1909 | E_err:   0.006826
[2025-10-07 18:18:03] [Iter  612/1050] R2[161/600]   | LR: 0.025815 | E:  -46.817360 | E_var:     0.1399 | E_err:   0.005845
[2025-10-07 18:18:11] [Iter  613/1050] R2[162/600]   | LR: 0.025766 | E:  -46.833650 | E_var:     0.2007 | E_err:   0.006999
[2025-10-07 18:18:19] [Iter  614/1050] R2[163/600]   | LR: 0.025717 | E:  -46.809483 | E_var:     0.1460 | E_err:   0.005971
[2025-10-07 18:18:27] [Iter  615/1050] R2[164/600]   | LR: 0.025668 | E:  -46.817778 | E_var:     0.1704 | E_err:   0.006450
[2025-10-07 18:18:35] [Iter  616/1050] R2[165/600]   | LR: 0.025618 | E:  -46.822896 | E_var:     0.1986 | E_err:   0.006963
[2025-10-07 18:18:42] [Iter  617/1050] R2[166/600]   | LR: 0.025568 | E:  -46.818788 | E_var:     0.2230 | E_err:   0.007379
[2025-10-07 18:18:50] [Iter  618/1050] R2[167/600]   | LR: 0.025518 | E:  -46.817139 | E_var:     0.1520 | E_err:   0.006091
[2025-10-07 18:18:58] [Iter  619/1050] R2[168/600]   | LR: 0.025468 | E:  -46.801459 | E_var:     0.1740 | E_err:   0.006517
[2025-10-07 18:19:06] [Iter  620/1050] R2[169/600]   | LR: 0.025417 | E:  -46.810768 | E_var:     0.2279 | E_err:   0.007458
[2025-10-07 18:19:14] [Iter  621/1050] R2[170/600]   | LR: 0.025367 | E:  -46.803556 | E_var:     0.1804 | E_err:   0.006637
[2025-10-07 18:19:21] [Iter  622/1050] R2[171/600]   | LR: 0.025316 | E:  -46.816750 | E_var:     0.1637 | E_err:   0.006321
[2025-10-07 18:19:29] [Iter  623/1050] R2[172/600]   | LR: 0.025264 | E:  -46.812320 | E_var:     0.3726 | E_err:   0.009537
[2025-10-07 18:19:37] [Iter  624/1050] R2[173/600]   | LR: 0.025213 | E:  -46.806733 | E_var:     0.1535 | E_err:   0.006121
[2025-10-07 18:19:45] [Iter  625/1050] R2[174/600]   | LR: 0.025161 | E:  -46.813746 | E_var:     0.1599 | E_err:   0.006248
[2025-10-07 18:19:53] [Iter  626/1050] R2[175/600]   | LR: 0.025110 | E:  -46.803532 | E_var:     0.1777 | E_err:   0.006586
[2025-10-07 18:20:00] [Iter  627/1050] R2[176/600]   | LR: 0.025057 | E:  -46.816450 | E_var:     0.2869 | E_err:   0.008370
[2025-10-07 18:20:08] [Iter  628/1050] R2[177/600]   | LR: 0.025005 | E:  -46.805775 | E_var:     0.2392 | E_err:   0.007642
[2025-10-07 18:20:16] [Iter  629/1050] R2[178/600]   | LR: 0.024953 | E:  -46.818921 | E_var:     0.1336 | E_err:   0.005711
[2025-10-07 18:20:24] [Iter  630/1050] R2[179/600]   | LR: 0.024900 | E:  -46.804712 | E_var:     0.1642 | E_err:   0.006331
[2025-10-07 18:20:31] [Iter  631/1050] R2[180/600]   | LR: 0.024847 | E:  -46.820498 | E_var:     0.4212 | E_err:   0.010141
[2025-10-07 18:20:39] [Iter  632/1050] R2[181/600]   | LR: 0.024794 | E:  -46.805618 | E_var:     0.1798 | E_err:   0.006625
[2025-10-07 18:20:47] [Iter  633/1050] R2[182/600]   | LR: 0.024741 | E:  -46.814898 | E_var:     0.1988 | E_err:   0.006966
[2025-10-07 18:20:55] [Iter  634/1050] R2[183/600]   | LR: 0.024688 | E:  -46.819716 | E_var:     0.1586 | E_err:   0.006223
[2025-10-07 18:21:03] [Iter  635/1050] R2[184/600]   | LR: 0.024634 | E:  -46.802905 | E_var:     0.2014 | E_err:   0.007012
[2025-10-07 18:21:10] [Iter  636/1050] R2[185/600]   | LR: 0.024580 | E:  -46.816356 | E_var:     0.1298 | E_err:   0.005630
[2025-10-07 18:21:18] [Iter  637/1050] R2[186/600]   | LR: 0.024526 | E:  -46.816529 | E_var:     0.1543 | E_err:   0.006137
[2025-10-07 18:21:26] [Iter  638/1050] R2[187/600]   | LR: 0.024472 | E:  -46.814783 | E_var:     0.1626 | E_err:   0.006300
[2025-10-07 18:21:34] [Iter  639/1050] R2[188/600]   | LR: 0.024417 | E:  -46.803972 | E_var:     0.1599 | E_err:   0.006249
[2025-10-07 18:21:42] [Iter  640/1050] R2[189/600]   | LR: 0.024363 | E:  -46.814465 | E_var:     0.1889 | E_err:   0.006791
[2025-10-07 18:21:49] [Iter  641/1050] R2[190/600]   | LR: 0.024308 | E:  -46.812898 | E_var:     0.1650 | E_err:   0.006347
[2025-10-07 18:21:57] [Iter  642/1050] R2[191/600]   | LR: 0.024253 | E:  -46.806646 | E_var:     0.1623 | E_err:   0.006295
[2025-10-07 18:22:05] [Iter  643/1050] R2[192/600]   | LR: 0.024198 | E:  -46.811350 | E_var:     0.1820 | E_err:   0.006665
[2025-10-07 18:22:13] [Iter  644/1050] R2[193/600]   | LR: 0.024142 | E:  -46.815550 | E_var:     0.1982 | E_err:   0.006956
[2025-10-07 18:22:21] [Iter  645/1050] R2[194/600]   | LR: 0.024087 | E:  -46.819894 | E_var:     0.1622 | E_err:   0.006293
[2025-10-07 18:22:28] [Iter  646/1050] R2[195/600]   | LR: 0.024031 | E:  -46.803432 | E_var:     0.2120 | E_err:   0.007195
[2025-10-07 18:22:36] [Iter  647/1050] R2[196/600]   | LR: 0.023975 | E:  -46.809805 | E_var:     0.1635 | E_err:   0.006318
[2025-10-07 18:22:44] [Iter  648/1050] R2[197/600]   | LR: 0.023919 | E:  -46.815437 | E_var:     0.1592 | E_err:   0.006235
[2025-10-07 18:22:52] [Iter  649/1050] R2[198/600]   | LR: 0.023863 | E:  -46.818493 | E_var:     0.2006 | E_err:   0.006998
[2025-10-07 18:23:00] [Iter  650/1050] R2[199/600]   | LR: 0.023807 | E:  -46.809021 | E_var:     0.2131 | E_err:   0.007213
[2025-10-07 18:23:07] [Iter  651/1050] R2[200/600]   | LR: 0.023750 | E:  -46.824264 | E_var:     0.2322 | E_err:   0.007529
[2025-10-07 18:23:15] [Iter  652/1050] R2[201/600]   | LR: 0.023693 | E:  -46.816458 | E_var:     0.1378 | E_err:   0.005800
[2025-10-07 18:23:23] [Iter  653/1050] R2[202/600]   | LR: 0.023636 | E:  -46.816938 | E_var:     0.1766 | E_err:   0.006567
[2025-10-07 18:23:31] [Iter  654/1050] R2[203/600]   | LR: 0.023579 | E:  -46.810497 | E_var:     0.1682 | E_err:   0.006408
[2025-10-07 18:23:38] [Iter  655/1050] R2[204/600]   | LR: 0.023522 | E:  -46.822530 | E_var:     0.1505 | E_err:   0.006061
[2025-10-07 18:23:46] [Iter  656/1050] R2[205/600]   | LR: 0.023464 | E:  -46.812348 | E_var:     0.1531 | E_err:   0.006115
[2025-10-07 18:23:54] [Iter  657/1050] R2[206/600]   | LR: 0.023407 | E:  -46.822182 | E_var:     0.2235 | E_err:   0.007387
[2025-10-07 18:24:02] [Iter  658/1050] R2[207/600]   | LR: 0.023349 | E:  -46.809626 | E_var:     0.1515 | E_err:   0.006082
[2025-10-07 18:24:10] [Iter  659/1050] R2[208/600]   | LR: 0.023291 | E:  -46.825018 | E_var:     0.1677 | E_err:   0.006399
[2025-10-07 18:24:17] [Iter  660/1050] R2[209/600]   | LR: 0.023233 | E:  -46.812177 | E_var:     0.1697 | E_err:   0.006437
[2025-10-07 18:24:25] [Iter  661/1050] R2[210/600]   | LR: 0.023175 | E:  -46.811838 | E_var:     0.1589 | E_err:   0.006229
[2025-10-07 18:24:33] [Iter  662/1050] R2[211/600]   | LR: 0.023116 | E:  -46.820476 | E_var:     0.1613 | E_err:   0.006275
[2025-10-07 18:24:41] [Iter  663/1050] R2[212/600]   | LR: 0.023058 | E:  -46.809552 | E_var:     0.1905 | E_err:   0.006820
[2025-10-07 18:24:49] [Iter  664/1050] R2[213/600]   | LR: 0.022999 | E:  -46.810870 | E_var:     0.1706 | E_err:   0.006454
[2025-10-07 18:24:56] [Iter  665/1050] R2[214/600]   | LR: 0.022940 | E:  -46.816199 | E_var:     0.1606 | E_err:   0.006262
[2025-10-07 18:25:04] [Iter  666/1050] R2[215/600]   | LR: 0.022881 | E:  -46.809272 | E_var:     0.2872 | E_err:   0.008373
[2025-10-07 18:25:12] [Iter  667/1050] R2[216/600]   | LR: 0.022822 | E:  -46.822807 | E_var:     0.2250 | E_err:   0.007412
[2025-10-07 18:25:20] [Iter  668/1050] R2[217/600]   | LR: 0.022763 | E:  -46.811428 | E_var:     0.1613 | E_err:   0.006275
[2025-10-07 18:25:28] [Iter  669/1050] R2[218/600]   | LR: 0.022704 | E:  -46.815717 | E_var:     0.1467 | E_err:   0.005984
[2025-10-07 18:25:36] [Iter  670/1050] R2[219/600]   | LR: 0.022644 | E:  -46.818186 | E_var:     0.1528 | E_err:   0.006109
[2025-10-07 18:25:44] [Iter  671/1050] R2[220/600]   | LR: 0.022584 | E:  -46.810683 | E_var:     0.1999 | E_err:   0.006986
[2025-10-07 18:25:51] [Iter  672/1050] R2[221/600]   | LR: 0.022524 | E:  -46.812906 | E_var:     0.2503 | E_err:   0.007818
[2025-10-07 18:25:59] [Iter  673/1050] R2[222/600]   | LR: 0.022464 | E:  -46.818407 | E_var:     0.1642 | E_err:   0.006331
[2025-10-07 18:26:07] [Iter  674/1050] R2[223/600]   | LR: 0.022404 | E:  -46.806942 | E_var:     0.1555 | E_err:   0.006161
[2025-10-07 18:26:15] [Iter  675/1050] R2[224/600]   | LR: 0.022344 | E:  -46.799470 | E_var:     0.3080 | E_err:   0.008672
[2025-10-07 18:26:23] [Iter  676/1050] R2[225/600]   | LR: 0.022284 | E:  -46.815659 | E_var:     0.1590 | E_err:   0.006231
[2025-10-07 18:26:30] [Iter  677/1050] R2[226/600]   | LR: 0.022223 | E:  -46.805721 | E_var:     0.1631 | E_err:   0.006311
[2025-10-07 18:26:38] [Iter  678/1050] R2[227/600]   | LR: 0.022162 | E:  -46.816637 | E_var:     0.1543 | E_err:   0.006138
[2025-10-07 18:26:46] [Iter  679/1050] R2[228/600]   | LR: 0.022102 | E:  -46.821929 | E_var:     0.1381 | E_err:   0.005807
[2025-10-07 18:26:54] [Iter  680/1050] R2[229/600]   | LR: 0.022041 | E:  -46.813176 | E_var:     0.1681 | E_err:   0.006407
[2025-10-07 18:27:02] [Iter  681/1050] R2[230/600]   | LR: 0.021980 | E:  -46.811983 | E_var:     0.1821 | E_err:   0.006667
[2025-10-07 18:27:09] [Iter  682/1050] R2[231/600]   | LR: 0.021918 | E:  -46.807143 | E_var:     0.1625 | E_err:   0.006299
[2025-10-07 18:27:17] [Iter  683/1050] R2[232/600]   | LR: 0.021857 | E:  -46.819425 | E_var:     0.2434 | E_err:   0.007709
[2025-10-07 18:27:25] [Iter  684/1050] R2[233/600]   | LR: 0.021796 | E:  -46.813767 | E_var:     0.1567 | E_err:   0.006185
[2025-10-07 18:27:33] [Iter  685/1050] R2[234/600]   | LR: 0.021734 | E:  -46.810368 | E_var:     0.1904 | E_err:   0.006818
[2025-10-07 18:27:41] [Iter  686/1050] R2[235/600]   | LR: 0.021673 | E:  -46.819290 | E_var:     0.2732 | E_err:   0.008167
[2025-10-07 18:27:48] [Iter  687/1050] R2[236/600]   | LR: 0.021611 | E:  -46.817217 | E_var:     0.1957 | E_err:   0.006912
[2025-10-07 18:27:56] [Iter  688/1050] R2[237/600]   | LR: 0.021549 | E:  -46.809032 | E_var:     0.1685 | E_err:   0.006414
[2025-10-07 18:28:04] [Iter  689/1050] R2[238/600]   | LR: 0.021487 | E:  -46.820623 | E_var:     0.1532 | E_err:   0.006116
[2025-10-07 18:28:12] [Iter  690/1050] R2[239/600]   | LR: 0.021425 | E:  -46.816785 | E_var:     0.1945 | E_err:   0.006890
[2025-10-07 18:28:20] [Iter  691/1050] R2[240/600]   | LR: 0.021363 | E:  -46.815276 | E_var:     0.3938 | E_err:   0.009805
[2025-10-07 18:28:27] [Iter  692/1050] R2[241/600]   | LR: 0.021300 | E:  -46.798039 | E_var:     0.1754 | E_err:   0.006543
[2025-10-07 18:28:35] [Iter  693/1050] R2[242/600]   | LR: 0.021238 | E:  -46.814548 | E_var:     0.2146 | E_err:   0.007239
[2025-10-07 18:28:43] [Iter  694/1050] R2[243/600]   | LR: 0.021176 | E:  -46.805874 | E_var:     0.2352 | E_err:   0.007578
[2025-10-07 18:28:51] [Iter  695/1050] R2[244/600]   | LR: 0.021113 | E:  -46.824745 | E_var:     0.1620 | E_err:   0.006288
[2025-10-07 18:28:59] [Iter  696/1050] R2[245/600]   | LR: 0.021050 | E:  -46.809818 | E_var:     0.2375 | E_err:   0.007614
[2025-10-07 18:29:06] [Iter  697/1050] R2[246/600]   | LR: 0.020987 | E:  -46.818994 | E_var:     0.1960 | E_err:   0.006918
[2025-10-07 18:29:14] [Iter  698/1050] R2[247/600]   | LR: 0.020924 | E:  -46.806541 | E_var:     0.1756 | E_err:   0.006548
[2025-10-07 18:29:22] [Iter  699/1050] R2[248/600]   | LR: 0.020861 | E:  -46.821001 | E_var:     0.1991 | E_err:   0.006973
[2025-10-07 18:29:30] [Iter  700/1050] R2[249/600]   | LR: 0.020798 | E:  -46.810298 | E_var:     0.1594 | E_err:   0.006239
[2025-10-07 18:29:30] ✓ Checkpoint saved: checkpoint_iter_000700.pkl
[2025-10-07 18:29:38] [Iter  701/1050] R2[250/600]   | LR: 0.020735 | E:  -46.808697 | E_var:     0.1482 | E_err:   0.006015
[2025-10-07 18:29:46] [Iter  702/1050] R2[251/600]   | LR: 0.020672 | E:  -46.792949 | E_var:     0.3021 | E_err:   0.008588
[2025-10-07 18:29:54] [Iter  703/1050] R2[252/600]   | LR: 0.020609 | E:  -46.805739 | E_var:     0.2082 | E_err:   0.007129
[2025-10-07 18:30:01] [Iter  704/1050] R2[253/600]   | LR: 0.020545 | E:  -46.809688 | E_var:     0.1954 | E_err:   0.006907
[2025-10-07 18:30:09] [Iter  705/1050] R2[254/600]   | LR: 0.020482 | E:  -46.817489 | E_var:     1.0650 | E_err:   0.016124
[2025-10-07 18:30:17] [Iter  706/1050] R2[255/600]   | LR: 0.020418 | E:  -46.817987 | E_var:     0.1712 | E_err:   0.006466
[2025-10-07 18:30:25] [Iter  707/1050] R2[256/600]   | LR: 0.020354 | E:  -46.816261 | E_var:     0.1685 | E_err:   0.006414
[2025-10-07 18:30:33] [Iter  708/1050] R2[257/600]   | LR: 0.020291 | E:  -46.825911 | E_var:     0.2037 | E_err:   0.007052
[2025-10-07 18:30:40] [Iter  709/1050] R2[258/600]   | LR: 0.020227 | E:  -46.809886 | E_var:     0.1961 | E_err:   0.006920
[2025-10-07 18:30:48] [Iter  710/1050] R2[259/600]   | LR: 0.020163 | E:  -46.815148 | E_var:     0.1663 | E_err:   0.006372
[2025-10-07 18:30:56] [Iter  711/1050] R2[260/600]   | LR: 0.020099 | E:  -46.818676 | E_var:     0.1647 | E_err:   0.006340
[2025-10-07 18:31:04] [Iter  712/1050] R2[261/600]   | LR: 0.020035 | E:  -46.817617 | E_var:     0.1936 | E_err:   0.006875
[2025-10-07 18:31:12] [Iter  713/1050] R2[262/600]   | LR: 0.019971 | E:  -46.812476 | E_var:     0.1489 | E_err:   0.006029
[2025-10-07 18:31:19] [Iter  714/1050] R2[263/600]   | LR: 0.019907 | E:  -46.806786 | E_var:     0.1678 | E_err:   0.006401
[2025-10-07 18:31:27] [Iter  715/1050] R2[264/600]   | LR: 0.019842 | E:  -46.810137 | E_var:     0.1714 | E_err:   0.006470
[2025-10-07 18:31:35] [Iter  716/1050] R2[265/600]   | LR: 0.019778 | E:  -46.810223 | E_var:     0.1841 | E_err:   0.006705
[2025-10-07 18:31:43] [Iter  717/1050] R2[266/600]   | LR: 0.019714 | E:  -46.829289 | E_var:     0.1629 | E_err:   0.006307
[2025-10-07 18:31:51] [Iter  718/1050] R2[267/600]   | LR: 0.019649 | E:  -46.793699 | E_var:     0.2200 | E_err:   0.007329
[2025-10-07 18:31:58] [Iter  719/1050] R2[268/600]   | LR: 0.019585 | E:  -46.806110 | E_var:     0.1473 | E_err:   0.005996
[2025-10-07 18:32:06] [Iter  720/1050] R2[269/600]   | LR: 0.019520 | E:  -46.814322 | E_var:     0.2157 | E_err:   0.007257
[2025-10-07 18:32:14] [Iter  721/1050] R2[270/600]   | LR: 0.019455 | E:  -46.832328 | E_var:     0.1778 | E_err:   0.006589
[2025-10-07 18:32:22] [Iter  722/1050] R2[271/600]   | LR: 0.019391 | E:  -46.821131 | E_var:     0.1522 | E_err:   0.006096
[2025-10-07 18:32:30] [Iter  723/1050] R2[272/600]   | LR: 0.019326 | E:  -46.809680 | E_var:     0.1513 | E_err:   0.006078
[2025-10-07 18:32:37] [Iter  724/1050] R2[273/600]   | LR: 0.019261 | E:  -46.815584 | E_var:     0.1734 | E_err:   0.006506
[2025-10-07 18:32:45] [Iter  725/1050] R2[274/600]   | LR: 0.019196 | E:  -46.811171 | E_var:     0.1881 | E_err:   0.006777
[2025-10-07 18:32:53] [Iter  726/1050] R2[275/600]   | LR: 0.019132 | E:  -46.804521 | E_var:     0.1759 | E_err:   0.006553
[2025-10-07 18:33:01] [Iter  727/1050] R2[276/600]   | LR: 0.019067 | E:  -46.802005 | E_var:     0.2432 | E_err:   0.007706
[2025-10-07 18:33:09] [Iter  728/1050] R2[277/600]   | LR: 0.019002 | E:  -46.807865 | E_var:     0.2213 | E_err:   0.007350
[2025-10-07 18:33:16] [Iter  729/1050] R2[278/600]   | LR: 0.018937 | E:  -46.816048 | E_var:     0.1466 | E_err:   0.005982
[2025-10-07 18:33:24] [Iter  730/1050] R2[279/600]   | LR: 0.018872 | E:  -46.818279 | E_var:     0.2319 | E_err:   0.007524
[2025-10-07 18:33:32] [Iter  731/1050] R2[280/600]   | LR: 0.018807 | E:  -46.808276 | E_var:     0.2216 | E_err:   0.007355
[2025-10-07 18:33:40] [Iter  732/1050] R2[281/600]   | LR: 0.018741 | E:  -46.816504 | E_var:     0.3298 | E_err:   0.008973
[2025-10-07 18:33:47] [Iter  733/1050] R2[282/600]   | LR: 0.018676 | E:  -46.806967 | E_var:     0.1451 | E_err:   0.005951
[2025-10-07 18:33:55] [Iter  734/1050] R2[283/600]   | LR: 0.018611 | E:  -46.809352 | E_var:     0.1995 | E_err:   0.006978
[2025-10-07 18:34:03] [Iter  735/1050] R2[284/600]   | LR: 0.018546 | E:  -46.807562 | E_var:     0.1632 | E_err:   0.006311
[2025-10-07 18:34:11] [Iter  736/1050] R2[285/600]   | LR: 0.018481 | E:  -46.811825 | E_var:     0.1812 | E_err:   0.006652
[2025-10-07 18:34:19] [Iter  737/1050] R2[286/600]   | LR: 0.018415 | E:  -46.808461 | E_var:     0.1654 | E_err:   0.006356
[2025-10-07 18:34:26] [Iter  738/1050] R2[287/600]   | LR: 0.018350 | E:  -46.809812 | E_var:     0.1534 | E_err:   0.006120
[2025-10-07 18:34:34] [Iter  739/1050] R2[288/600]   | LR: 0.018285 | E:  -46.809443 | E_var:     0.1859 | E_err:   0.006736
[2025-10-07 18:34:42] [Iter  740/1050] R2[289/600]   | LR: 0.018220 | E:  -46.816466 | E_var:     0.1789 | E_err:   0.006609
[2025-10-07 18:34:50] [Iter  741/1050] R2[290/600]   | LR: 0.018154 | E:  -46.817427 | E_var:     0.1543 | E_err:   0.006138
[2025-10-07 18:34:58] [Iter  742/1050] R2[291/600]   | LR: 0.018089 | E:  -46.804459 | E_var:     0.2242 | E_err:   0.007399
[2025-10-07 18:35:05] [Iter  743/1050] R2[292/600]   | LR: 0.018023 | E:  -46.803190 | E_var:     0.1305 | E_err:   0.005645
[2025-10-07 18:35:13] [Iter  744/1050] R2[293/600]   | LR: 0.017958 | E:  -46.812795 | E_var:     0.2108 | E_err:   0.007174
[2025-10-07 18:35:21] [Iter  745/1050] R2[294/600]   | LR: 0.017893 | E:  -46.805336 | E_var:     0.1874 | E_err:   0.006764
[2025-10-07 18:35:29] [Iter  746/1050] R2[295/600]   | LR: 0.017827 | E:  -46.797363 | E_var:     0.1708 | E_err:   0.006458
[2025-10-07 18:35:37] [Iter  747/1050] R2[296/600]   | LR: 0.017762 | E:  -46.811373 | E_var:     0.1536 | E_err:   0.006123
[2025-10-07 18:35:44] [Iter  748/1050] R2[297/600]   | LR: 0.017696 | E:  -46.808385 | E_var:     0.2588 | E_err:   0.007949
[2025-10-07 18:35:52] [Iter  749/1050] R2[298/600]   | LR: 0.017631 | E:  -46.806877 | E_var:     0.1814 | E_err:   0.006655
[2025-10-07 18:36:00] [Iter  750/1050] R2[299/600]   | LR: 0.017565 | E:  -46.809080 | E_var:     0.1696 | E_err:   0.006435
[2025-10-07 18:36:08] [Iter  751/1050] R2[300/600]   | LR: 0.017500 | E:  -46.813238 | E_var:     0.2047 | E_err:   0.007069
[2025-10-07 18:36:16] [Iter  752/1050] R2[301/600]   | LR: 0.017435 | E:  -46.819434 | E_var:     0.1426 | E_err:   0.005899
[2025-10-07 18:36:23] [Iter  753/1050] R2[302/600]   | LR: 0.017369 | E:  -46.815607 | E_var:     0.2240 | E_err:   0.007395
[2025-10-07 18:36:31] [Iter  754/1050] R2[303/600]   | LR: 0.017304 | E:  -46.809730 | E_var:     0.1398 | E_err:   0.005843
[2025-10-07 18:36:39] [Iter  755/1050] R2[304/600]   | LR: 0.017238 | E:  -46.818317 | E_var:     0.1597 | E_err:   0.006243
[2025-10-07 18:36:47] [Iter  756/1050] R2[305/600]   | LR: 0.017173 | E:  -46.800547 | E_var:     0.2411 | E_err:   0.007672
[2025-10-07 18:36:55] [Iter  757/1050] R2[306/600]   | LR: 0.017107 | E:  -46.823621 | E_var:     0.1675 | E_err:   0.006394
[2025-10-07 18:37:02] [Iter  758/1050] R2[307/600]   | LR: 0.017042 | E:  -46.812240 | E_var:     0.1408 | E_err:   0.005862
[2025-10-07 18:37:10] [Iter  759/1050] R2[308/600]   | LR: 0.016977 | E:  -46.803937 | E_var:     0.1600 | E_err:   0.006250
[2025-10-07 18:37:18] [Iter  760/1050] R2[309/600]   | LR: 0.016911 | E:  -46.817784 | E_var:     0.2117 | E_err:   0.007189
[2025-10-07 18:37:26] [Iter  761/1050] R2[310/600]   | LR: 0.016846 | E:  -46.802179 | E_var:     0.1878 | E_err:   0.006771
[2025-10-07 18:37:34] [Iter  762/1050] R2[311/600]   | LR: 0.016780 | E:  -46.813724 | E_var:     0.1960 | E_err:   0.006917
[2025-10-07 18:37:41] [Iter  763/1050] R2[312/600]   | LR: 0.016715 | E:  -46.816811 | E_var:     0.1861 | E_err:   0.006741
[2025-10-07 18:37:49] [Iter  764/1050] R2[313/600]   | LR: 0.016650 | E:  -46.816630 | E_var:     0.2158 | E_err:   0.007258
[2025-10-07 18:37:57] [Iter  765/1050] R2[314/600]   | LR: 0.016585 | E:  -46.810001 | E_var:     0.1716 | E_err:   0.006472
[2025-10-07 18:38:05] [Iter  766/1050] R2[315/600]   | LR: 0.016519 | E:  -46.811908 | E_var:     0.1761 | E_err:   0.006556
[2025-10-07 18:38:13] [Iter  767/1050] R2[316/600]   | LR: 0.016454 | E:  -46.815159 | E_var:     0.1633 | E_err:   0.006314
[2025-10-07 18:38:20] [Iter  768/1050] R2[317/600]   | LR: 0.016389 | E:  -46.815693 | E_var:     0.1759 | E_err:   0.006553
[2025-10-07 18:38:28] [Iter  769/1050] R2[318/600]   | LR: 0.016324 | E:  -46.803342 | E_var:     0.2943 | E_err:   0.008476
[2025-10-07 18:38:36] [Iter  770/1050] R2[319/600]   | LR: 0.016259 | E:  -46.804062 | E_var:     0.1451 | E_err:   0.005953
[2025-10-07 18:38:44] [Iter  771/1050] R2[320/600]   | LR: 0.016193 | E:  -46.813019 | E_var:     0.2105 | E_err:   0.007169
[2025-10-07 18:38:52] [Iter  772/1050] R2[321/600]   | LR: 0.016128 | E:  -46.808705 | E_var:     0.1782 | E_err:   0.006596
[2025-10-07 18:38:59] [Iter  773/1050] R2[322/600]   | LR: 0.016063 | E:  -46.803998 | E_var:     0.2011 | E_err:   0.007007
[2025-10-07 18:39:07] [Iter  774/1050] R2[323/600]   | LR: 0.015998 | E:  -46.818073 | E_var:     0.1806 | E_err:   0.006640
[2025-10-07 18:39:15] [Iter  775/1050] R2[324/600]   | LR: 0.015933 | E:  -46.805605 | E_var:     0.1394 | E_err:   0.005835
[2025-10-07 18:39:23] [Iter  776/1050] R2[325/600]   | LR: 0.015868 | E:  -46.819003 | E_var:     0.1505 | E_err:   0.006062
[2025-10-07 18:39:31] [Iter  777/1050] R2[326/600]   | LR: 0.015804 | E:  -46.812974 | E_var:     0.1787 | E_err:   0.006605
[2025-10-07 18:39:38] [Iter  778/1050] R2[327/600]   | LR: 0.015739 | E:  -46.806349 | E_var:     0.1824 | E_err:   0.006674
[2025-10-07 18:39:46] [Iter  779/1050] R2[328/600]   | LR: 0.015674 | E:  -46.815563 | E_var:     0.1522 | E_err:   0.006095
[2025-10-07 18:39:54] [Iter  780/1050] R2[329/600]   | LR: 0.015609 | E:  -46.817679 | E_var:     0.1542 | E_err:   0.006136
[2025-10-07 18:40:02] [Iter  781/1050] R2[330/600]   | LR: 0.015545 | E:  -46.819638 | E_var:     0.2136 | E_err:   0.007222
[2025-10-07 18:40:10] [Iter  782/1050] R2[331/600]   | LR: 0.015480 | E:  -46.822873 | E_var:     0.2827 | E_err:   0.008308
[2025-10-07 18:40:17] [Iter  783/1050] R2[332/600]   | LR: 0.015415 | E:  -46.806327 | E_var:     0.1756 | E_err:   0.006548
[2025-10-07 18:40:25] [Iter  784/1050] R2[333/600]   | LR: 0.015351 | E:  -46.796547 | E_var:     0.2041 | E_err:   0.007058
[2025-10-07 18:40:33] [Iter  785/1050] R2[334/600]   | LR: 0.015286 | E:  -46.817544 | E_var:     0.1564 | E_err:   0.006180
[2025-10-07 18:40:41] [Iter  786/1050] R2[335/600]   | LR: 0.015222 | E:  -46.824028 | E_var:     0.1611 | E_err:   0.006271
[2025-10-07 18:40:49] [Iter  787/1050] R2[336/600]   | LR: 0.015158 | E:  -46.814420 | E_var:     0.1627 | E_err:   0.006303
[2025-10-07 18:40:56] [Iter  788/1050] R2[337/600]   | LR: 0.015093 | E:  -46.818372 | E_var:     0.1528 | E_err:   0.006108
[2025-10-07 18:41:04] [Iter  789/1050] R2[338/600]   | LR: 0.015029 | E:  -46.811731 | E_var:     0.2429 | E_err:   0.007700
[2025-10-07 18:41:12] [Iter  790/1050] R2[339/600]   | LR: 0.014965 | E:  -46.805414 | E_var:     0.1812 | E_err:   0.006651
[2025-10-07 18:41:20] [Iter  791/1050] R2[340/600]   | LR: 0.014901 | E:  -46.813333 | E_var:     0.2133 | E_err:   0.007217
[2025-10-07 18:41:28] [Iter  792/1050] R2[341/600]   | LR: 0.014837 | E:  -46.813170 | E_var:     0.1480 | E_err:   0.006010
[2025-10-07 18:41:35] [Iter  793/1050] R2[342/600]   | LR: 0.014773 | E:  -46.826124 | E_var:     0.1698 | E_err:   0.006439
[2025-10-07 18:41:43] [Iter  794/1050] R2[343/600]   | LR: 0.014709 | E:  -46.812018 | E_var:     0.1499 | E_err:   0.006049
[2025-10-07 18:41:51] [Iter  795/1050] R2[344/600]   | LR: 0.014646 | E:  -46.818785 | E_var:     0.1667 | E_err:   0.006380
[2025-10-07 18:41:59] [Iter  796/1050] R2[345/600]   | LR: 0.014582 | E:  -46.809793 | E_var:     0.1754 | E_err:   0.006544
[2025-10-07 18:42:06] [Iter  797/1050] R2[346/600]   | LR: 0.014518 | E:  -46.812029 | E_var:     0.1641 | E_err:   0.006329
[2025-10-07 18:42:14] [Iter  798/1050] R2[347/600]   | LR: 0.014455 | E:  -46.806593 | E_var:     0.1686 | E_err:   0.006417
[2025-10-07 18:42:22] [Iter  799/1050] R2[348/600]   | LR: 0.014391 | E:  -46.808704 | E_var:     0.1758 | E_err:   0.006551
[2025-10-07 18:42:30] [Iter  800/1050] R2[349/600]   | LR: 0.014328 | E:  -46.812691 | E_var:     0.1474 | E_err:   0.005998
[2025-10-07 18:42:30] ✓ Checkpoint saved: checkpoint_iter_000800.pkl
[2025-10-07 18:42:38] [Iter  801/1050] R2[350/600]   | LR: 0.014265 | E:  -46.807668 | E_var:     0.1996 | E_err:   0.006982
[2025-10-07 18:42:46] [Iter  802/1050] R2[351/600]   | LR: 0.014202 | E:  -46.819028 | E_var:     0.1312 | E_err:   0.005660
[2025-10-07 18:42:53] [Iter  803/1050] R2[352/600]   | LR: 0.014139 | E:  -46.804120 | E_var:     0.2002 | E_err:   0.006990
[2025-10-07 18:43:01] [Iter  804/1050] R2[353/600]   | LR: 0.014076 | E:  -46.828805 | E_var:     0.2140 | E_err:   0.007227
[2025-10-07 18:43:09] [Iter  805/1050] R2[354/600]   | LR: 0.014013 | E:  -46.827672 | E_var:     0.1752 | E_err:   0.006540
[2025-10-07 18:43:17] [Iter  806/1050] R2[355/600]   | LR: 0.013950 | E:  -46.808518 | E_var:     0.1386 | E_err:   0.005816
[2025-10-07 18:43:25] [Iter  807/1050] R2[356/600]   | LR: 0.013887 | E:  -46.802126 | E_var:     0.2091 | E_err:   0.007146
[2025-10-07 18:43:32] [Iter  808/1050] R2[357/600]   | LR: 0.013824 | E:  -46.816038 | E_var:     0.3106 | E_err:   0.008707
[2025-10-07 18:43:40] [Iter  809/1050] R2[358/600]   | LR: 0.013762 | E:  -46.812334 | E_var:     0.1546 | E_err:   0.006144
[2025-10-07 18:43:48] [Iter  810/1050] R2[359/600]   | LR: 0.013700 | E:  -46.815191 | E_var:     0.1631 | E_err:   0.006310
[2025-10-07 18:43:56] [Iter  811/1050] R2[360/600]   | LR: 0.013637 | E:  -46.800390 | E_var:     0.1698 | E_err:   0.006438
[2025-10-07 18:44:04] [Iter  812/1050] R2[361/600]   | LR: 0.013575 | E:  -46.811343 | E_var:     0.1673 | E_err:   0.006392
[2025-10-07 18:44:11] [Iter  813/1050] R2[362/600]   | LR: 0.013513 | E:  -46.803209 | E_var:     0.1611 | E_err:   0.006271
[2025-10-07 18:44:19] [Iter  814/1050] R2[363/600]   | LR: 0.013451 | E:  -46.813456 | E_var:     0.1379 | E_err:   0.005802
[2025-10-07 18:44:27] [Iter  815/1050] R2[364/600]   | LR: 0.013389 | E:  -46.808566 | E_var:     0.1501 | E_err:   0.006055
[2025-10-07 18:44:35] [Iter  816/1050] R2[365/600]   | LR: 0.013327 | E:  -46.818686 | E_var:     0.1755 | E_err:   0.006546
[2025-10-07 18:44:43] [Iter  817/1050] R2[366/600]   | LR: 0.013266 | E:  -46.808115 | E_var:     0.1913 | E_err:   0.006835
[2025-10-07 18:44:50] [Iter  818/1050] R2[367/600]   | LR: 0.013204 | E:  -46.824459 | E_var:     0.1750 | E_err:   0.006536
[2025-10-07 18:44:58] [Iter  819/1050] R2[368/600]   | LR: 0.013143 | E:  -46.809518 | E_var:     0.1687 | E_err:   0.006418
[2025-10-07 18:45:06] [Iter  820/1050] R2[369/600]   | LR: 0.013082 | E:  -46.803572 | E_var:     0.2205 | E_err:   0.007337
[2025-10-07 18:45:14] [Iter  821/1050] R2[370/600]   | LR: 0.013020 | E:  -46.814989 | E_var:     0.1974 | E_err:   0.006943
[2025-10-07 18:45:21] [Iter  822/1050] R2[371/600]   | LR: 0.012959 | E:  -46.818541 | E_var:     0.1679 | E_err:   0.006403
[2025-10-07 18:45:29] [Iter  823/1050] R2[372/600]   | LR: 0.012898 | E:  -46.819059 | E_var:     0.2368 | E_err:   0.007603
[2025-10-07 18:45:37] [Iter  824/1050] R2[373/600]   | LR: 0.012838 | E:  -46.809352 | E_var:     0.1697 | E_err:   0.006436
[2025-10-07 18:45:45] [Iter  825/1050] R2[374/600]   | LR: 0.012777 | E:  -46.813727 | E_var:     0.1947 | E_err:   0.006895
[2025-10-07 18:45:53] [Iter  826/1050] R2[375/600]   | LR: 0.012716 | E:  -46.812163 | E_var:     0.1623 | E_err:   0.006295
[2025-10-07 18:46:00] [Iter  827/1050] R2[376/600]   | LR: 0.012656 | E:  -46.820158 | E_var:     0.1673 | E_err:   0.006390
[2025-10-07 18:46:08] [Iter  828/1050] R2[377/600]   | LR: 0.012596 | E:  -46.813018 | E_var:     0.1429 | E_err:   0.005907
[2025-10-07 18:46:16] [Iter  829/1050] R2[378/600]   | LR: 0.012536 | E:  -46.814279 | E_var:     0.1968 | E_err:   0.006931
[2025-10-07 18:46:24] [Iter  830/1050] R2[379/600]   | LR: 0.012476 | E:  -46.818175 | E_var:     0.1778 | E_err:   0.006589
[2025-10-07 18:46:32] [Iter  831/1050] R2[380/600]   | LR: 0.012416 | E:  -46.805377 | E_var:     0.1925 | E_err:   0.006855
[2025-10-07 18:46:39] [Iter  832/1050] R2[381/600]   | LR: 0.012356 | E:  -46.812228 | E_var:     0.1695 | E_err:   0.006433
[2025-10-07 18:46:47] [Iter  833/1050] R2[382/600]   | LR: 0.012296 | E:  -46.799640 | E_var:     0.1938 | E_err:   0.006878
[2025-10-07 18:46:55] [Iter  834/1050] R2[383/600]   | LR: 0.012237 | E:  -46.814430 | E_var:     0.2038 | E_err:   0.007054
[2025-10-07 18:47:03] [Iter  835/1050] R2[384/600]   | LR: 0.012178 | E:  -46.811442 | E_var:     0.1596 | E_err:   0.006242
[2025-10-07 18:47:11] [Iter  836/1050] R2[385/600]   | LR: 0.012119 | E:  -46.803425 | E_var:     0.2308 | E_err:   0.007506
[2025-10-07 18:47:18] [Iter  837/1050] R2[386/600]   | LR: 0.012060 | E:  -46.820577 | E_var:     0.1637 | E_err:   0.006322
[2025-10-07 18:47:26] [Iter  838/1050] R2[387/600]   | LR: 0.012001 | E:  -46.825298 | E_var:     0.1448 | E_err:   0.005945
[2025-10-07 18:47:34] [Iter  839/1050] R2[388/600]   | LR: 0.011942 | E:  -46.807875 | E_var:     0.1877 | E_err:   0.006769
[2025-10-07 18:47:42] [Iter  840/1050] R2[389/600]   | LR: 0.011884 | E:  -46.812978 | E_var:     0.1988 | E_err:   0.006968
[2025-10-07 18:47:50] [Iter  841/1050] R2[390/600]   | LR: 0.011825 | E:  -46.802026 | E_var:     0.2011 | E_err:   0.007006
[2025-10-07 18:47:57] [Iter  842/1050] R2[391/600]   | LR: 0.011767 | E:  -46.813045 | E_var:     0.1782 | E_err:   0.006597
[2025-10-07 18:48:05] [Iter  843/1050] R2[392/600]   | LR: 0.011709 | E:  -46.805567 | E_var:     0.1626 | E_err:   0.006301
[2025-10-07 18:48:13] [Iter  844/1050] R2[393/600]   | LR: 0.011651 | E:  -46.815949 | E_var:     0.1669 | E_err:   0.006384
[2025-10-07 18:48:21] [Iter  845/1050] R2[394/600]   | LR: 0.011593 | E:  -46.812844 | E_var:     0.1441 | E_err:   0.005931
[2025-10-07 18:48:29] [Iter  846/1050] R2[395/600]   | LR: 0.011536 | E:  -46.811519 | E_var:     0.1703 | E_err:   0.006448
[2025-10-07 18:48:36] [Iter  847/1050] R2[396/600]   | LR: 0.011478 | E:  -46.799957 | E_var:     0.2438 | E_err:   0.007714
[2025-10-07 18:48:44] [Iter  848/1050] R2[397/600]   | LR: 0.011421 | E:  -46.818994 | E_var:     0.1752 | E_err:   0.006541
[2025-10-07 18:48:52] [Iter  849/1050] R2[398/600]   | LR: 0.011364 | E:  -46.815305 | E_var:     0.1826 | E_err:   0.006678
[2025-10-07 18:49:00] [Iter  850/1050] R2[399/600]   | LR: 0.011307 | E:  -46.813384 | E_var:     0.1925 | E_err:   0.006856
[2025-10-07 18:49:08] [Iter  851/1050] R2[400/600]   | LR: 0.011250 | E:  -46.802136 | E_var:     0.1923 | E_err:   0.006852
[2025-10-07 18:49:15] [Iter  852/1050] R2[401/600]   | LR: 0.011193 | E:  -46.809952 | E_var:     0.2045 | E_err:   0.007066
[2025-10-07 18:49:23] [Iter  853/1050] R2[402/600]   | LR: 0.011137 | E:  -46.793882 | E_var:     0.2418 | E_err:   0.007684
[2025-10-07 18:49:31] [Iter  854/1050] R2[403/600]   | LR: 0.011081 | E:  -46.817296 | E_var:     0.1804 | E_err:   0.006636
[2025-10-07 18:49:39] [Iter  855/1050] R2[404/600]   | LR: 0.011025 | E:  -46.815070 | E_var:     0.1923 | E_err:   0.006851
[2025-10-07 18:49:47] [Iter  856/1050] R2[405/600]   | LR: 0.010969 | E:  -46.814286 | E_var:     0.1651 | E_err:   0.006349
[2025-10-07 18:49:54] [Iter  857/1050] R2[406/600]   | LR: 0.010913 | E:  -46.818875 | E_var:     0.1553 | E_err:   0.006157
[2025-10-07 18:50:02] [Iter  858/1050] R2[407/600]   | LR: 0.010858 | E:  -46.810622 | E_var:     0.1577 | E_err:   0.006204
[2025-10-07 18:50:10] [Iter  859/1050] R2[408/600]   | LR: 0.010802 | E:  -46.808324 | E_var:     0.1545 | E_err:   0.006142
[2025-10-07 18:50:18] [Iter  860/1050] R2[409/600]   | LR: 0.010747 | E:  -46.821073 | E_var:     0.2010 | E_err:   0.007005
[2025-10-07 18:50:26] [Iter  861/1050] R2[410/600]   | LR: 0.010692 | E:  -46.818619 | E_var:     0.2879 | E_err:   0.008383
[2025-10-07 18:50:33] [Iter  862/1050] R2[411/600]   | LR: 0.010637 | E:  -46.822553 | E_var:     0.1745 | E_err:   0.006528
[2025-10-07 18:50:41] [Iter  863/1050] R2[412/600]   | LR: 0.010583 | E:  -46.813377 | E_var:     0.1667 | E_err:   0.006380
[2025-10-07 18:50:49] [Iter  864/1050] R2[413/600]   | LR: 0.010528 | E:  -46.804789 | E_var:     0.1811 | E_err:   0.006650
[2025-10-07 18:50:57] [Iter  865/1050] R2[414/600]   | LR: 0.010474 | E:  -46.817910 | E_var:     0.2614 | E_err:   0.007989
[2025-10-07 18:51:04] [Iter  866/1050] R2[415/600]   | LR: 0.010420 | E:  -46.805512 | E_var:     0.1479 | E_err:   0.006008
[2025-10-07 18:51:12] [Iter  867/1050] R2[416/600]   | LR: 0.010366 | E:  -46.815546 | E_var:     0.2222 | E_err:   0.007366
[2025-10-07 18:51:20] [Iter  868/1050] R2[417/600]   | LR: 0.010312 | E:  -46.814798 | E_var:     0.2611 | E_err:   0.007984
[2025-10-07 18:51:28] [Iter  869/1050] R2[418/600]   | LR: 0.010259 | E:  -46.809029 | E_var:     0.1493 | E_err:   0.006038
[2025-10-07 18:51:36] [Iter  870/1050] R2[419/600]   | LR: 0.010206 | E:  -46.807442 | E_var:     0.1492 | E_err:   0.006035
[2025-10-07 18:51:43] [Iter  871/1050] R2[420/600]   | LR: 0.010153 | E:  -46.806816 | E_var:     0.1748 | E_err:   0.006532
[2025-10-07 18:51:51] [Iter  872/1050] R2[421/600]   | LR: 0.010100 | E:  -46.804112 | E_var:     0.1785 | E_err:   0.006601
[2025-10-07 18:51:59] [Iter  873/1050] R2[422/600]   | LR: 0.010047 | E:  -46.810697 | E_var:     0.1623 | E_err:   0.006296
[2025-10-07 18:52:07] [Iter  874/1050] R2[423/600]   | LR: 0.009995 | E:  -46.808838 | E_var:     0.1516 | E_err:   0.006084
[2025-10-07 18:52:15] [Iter  875/1050] R2[424/600]   | LR: 0.009943 | E:  -46.808010 | E_var:     0.1355 | E_err:   0.005752
[2025-10-07 18:52:22] [Iter  876/1050] R2[425/600]   | LR: 0.009890 | E:  -46.816699 | E_var:     0.1452 | E_err:   0.005953
[2025-10-07 18:52:30] [Iter  877/1050] R2[426/600]   | LR: 0.009839 | E:  -46.812869 | E_var:     0.1789 | E_err:   0.006609
[2025-10-07 18:52:38] [Iter  878/1050] R2[427/600]   | LR: 0.009787 | E:  -46.812370 | E_var:     0.1420 | E_err:   0.005887
[2025-10-07 18:52:46] [Iter  879/1050] R2[428/600]   | LR: 0.009736 | E:  -46.803782 | E_var:     0.2494 | E_err:   0.007803
[2025-10-07 18:52:54] [Iter  880/1050] R2[429/600]   | LR: 0.009684 | E:  -46.818077 | E_var:     0.1827 | E_err:   0.006678
[2025-10-07 18:53:01] [Iter  881/1050] R2[430/600]   | LR: 0.009633 | E:  -46.809916 | E_var:     0.2020 | E_err:   0.007023
[2025-10-07 18:53:09] [Iter  882/1050] R2[431/600]   | LR: 0.009583 | E:  -46.802633 | E_var:     0.1708 | E_err:   0.006457
[2025-10-07 18:53:17] [Iter  883/1050] R2[432/600]   | LR: 0.009532 | E:  -46.810646 | E_var:     0.1742 | E_err:   0.006522
[2025-10-07 18:53:25] [Iter  884/1050] R2[433/600]   | LR: 0.009482 | E:  -46.821405 | E_var:     0.2801 | E_err:   0.008270
[2025-10-07 18:53:33] [Iter  885/1050] R2[434/600]   | LR: 0.009432 | E:  -46.819269 | E_var:     0.1672 | E_err:   0.006389
[2025-10-07 18:53:40] [Iter  886/1050] R2[435/600]   | LR: 0.009382 | E:  -46.806822 | E_var:     0.1515 | E_err:   0.006082
[2025-10-07 18:53:48] [Iter  887/1050] R2[436/600]   | LR: 0.009332 | E:  -46.816342 | E_var:     0.1831 | E_err:   0.006686
[2025-10-07 18:53:56] [Iter  888/1050] R2[437/600]   | LR: 0.009283 | E:  -46.817307 | E_var:     0.1995 | E_err:   0.006978
[2025-10-07 18:54:04] [Iter  889/1050] R2[438/600]   | LR: 0.009234 | E:  -46.817604 | E_var:     0.2995 | E_err:   0.008551
[2025-10-07 18:54:12] [Iter  890/1050] R2[439/600]   | LR: 0.009185 | E:  -46.820965 | E_var:     0.2104 | E_err:   0.007168
[2025-10-07 18:54:19] [Iter  891/1050] R2[440/600]   | LR: 0.009136 | E:  -46.805704 | E_var:     0.1546 | E_err:   0.006144
[2025-10-07 18:54:27] [Iter  892/1050] R2[441/600]   | LR: 0.009087 | E:  -46.811602 | E_var:     0.1535 | E_err:   0.006121
[2025-10-07 18:54:35] [Iter  893/1050] R2[442/600]   | LR: 0.009039 | E:  -46.812596 | E_var:     0.1574 | E_err:   0.006200
[2025-10-07 18:54:43] [Iter  894/1050] R2[443/600]   | LR: 0.008991 | E:  -46.802608 | E_var:     0.2273 | E_err:   0.007450
[2025-10-07 18:54:51] [Iter  895/1050] R2[444/600]   | LR: 0.008943 | E:  -46.807631 | E_var:     0.1731 | E_err:   0.006501
[2025-10-07 18:54:58] [Iter  896/1050] R2[445/600]   | LR: 0.008896 | E:  -46.817224 | E_var:     0.1450 | E_err:   0.005950
[2025-10-07 18:55:06] [Iter  897/1050] R2[446/600]   | LR: 0.008848 | E:  -46.811817 | E_var:     0.1317 | E_err:   0.005671
[2025-10-07 18:55:14] [Iter  898/1050] R2[447/600]   | LR: 0.008801 | E:  -46.821110 | E_var:     0.1618 | E_err:   0.006285
[2025-10-07 18:55:22] [Iter  899/1050] R2[448/600]   | LR: 0.008754 | E:  -46.815226 | E_var:     0.1569 | E_err:   0.006190
[2025-10-07 18:55:30] [Iter  900/1050] R2[449/600]   | LR: 0.008708 | E:  -46.816793 | E_var:     0.1868 | E_err:   0.006753
[2025-10-07 18:55:30] ✓ Checkpoint saved: checkpoint_iter_000900.pkl
[2025-10-07 18:55:37] [Iter  901/1050] R2[450/600]   | LR: 0.008661 | E:  -46.824622 | E_var:     0.1356 | E_err:   0.005754
[2025-10-07 18:55:45] [Iter  902/1050] R2[451/600]   | LR: 0.008615 | E:  -46.817958 | E_var:     0.2039 | E_err:   0.007055
[2025-10-07 18:55:53] [Iter  903/1050] R2[452/600]   | LR: 0.008569 | E:  -46.804030 | E_var:     0.1670 | E_err:   0.006386
[2025-10-07 18:56:01] [Iter  904/1050] R2[453/600]   | LR: 0.008523 | E:  -46.819693 | E_var:     0.1703 | E_err:   0.006448
[2025-10-07 18:56:09] [Iter  905/1050] R2[454/600]   | LR: 0.008478 | E:  -46.823519 | E_var:     0.1623 | E_err:   0.006295
[2025-10-07 18:56:16] [Iter  906/1050] R2[455/600]   | LR: 0.008433 | E:  -46.802195 | E_var:     0.1316 | E_err:   0.005668
[2025-10-07 18:56:24] [Iter  907/1050] R2[456/600]   | LR: 0.008388 | E:  -46.813567 | E_var:     0.2153 | E_err:   0.007250
[2025-10-07 18:56:32] [Iter  908/1050] R2[457/600]   | LR: 0.008343 | E:  -46.807358 | E_var:     0.1860 | E_err:   0.006738
[2025-10-07 18:56:40] [Iter  909/1050] R2[458/600]   | LR: 0.008299 | E:  -46.819817 | E_var:     0.1455 | E_err:   0.005960
[2025-10-07 18:56:48] [Iter  910/1050] R2[459/600]   | LR: 0.008255 | E:  -46.814227 | E_var:     0.2516 | E_err:   0.007838
[2025-10-07 18:56:55] [Iter  911/1050] R2[460/600]   | LR: 0.008211 | E:  -46.814050 | E_var:     0.1579 | E_err:   0.006209
[2025-10-07 18:57:03] [Iter  912/1050] R2[461/600]   | LR: 0.008167 | E:  -46.820922 | E_var:     0.1913 | E_err:   0.006833
[2025-10-07 18:57:11] [Iter  913/1050] R2[462/600]   | LR: 0.008124 | E:  -46.817230 | E_var:     0.1517 | E_err:   0.006087
[2025-10-07 18:57:19] [Iter  914/1050] R2[463/600]   | LR: 0.008080 | E:  -46.817193 | E_var:     0.2154 | E_err:   0.007253
[2025-10-07 18:57:27] [Iter  915/1050] R2[464/600]   | LR: 0.008038 | E:  -46.810391 | E_var:     0.1751 | E_err:   0.006537
[2025-10-07 18:57:34] [Iter  916/1050] R2[465/600]   | LR: 0.007995 | E:  -46.816244 | E_var:     0.4528 | E_err:   0.010514
[2025-10-07 18:57:42] [Iter  917/1050] R2[466/600]   | LR: 0.007953 | E:  -46.811301 | E_var:     0.1797 | E_err:   0.006624
[2025-10-07 18:57:50] [Iter  918/1050] R2[467/600]   | LR: 0.007910 | E:  -46.810973 | E_var:     0.1510 | E_err:   0.006072
[2025-10-07 18:57:58] [Iter  919/1050] R2[468/600]   | LR: 0.007869 | E:  -46.815880 | E_var:     0.2367 | E_err:   0.007602
[2025-10-07 18:58:05] [Iter  920/1050] R2[469/600]   | LR: 0.007827 | E:  -46.800474 | E_var:     0.1568 | E_err:   0.006188
[2025-10-07 18:58:13] [Iter  921/1050] R2[470/600]   | LR: 0.007786 | E:  -46.807038 | E_var:     0.1982 | E_err:   0.006956
[2025-10-07 18:58:21] [Iter  922/1050] R2[471/600]   | LR: 0.007745 | E:  -46.815408 | E_var:     0.1656 | E_err:   0.006359
[2025-10-07 18:58:29] [Iter  923/1050] R2[472/600]   | LR: 0.007704 | E:  -46.805072 | E_var:     0.1580 | E_err:   0.006211
[2025-10-07 18:58:37] [Iter  924/1050] R2[473/600]   | LR: 0.007663 | E:  -46.814607 | E_var:     0.2398 | E_err:   0.007652
[2025-10-07 18:58:44] [Iter  925/1050] R2[474/600]   | LR: 0.007623 | E:  -46.814248 | E_var:     0.1726 | E_err:   0.006492
[2025-10-07 18:58:52] [Iter  926/1050] R2[475/600]   | LR: 0.007583 | E:  -46.805049 | E_var:     0.1971 | E_err:   0.006936
[2025-10-07 18:59:00] [Iter  927/1050] R2[476/600]   | LR: 0.007543 | E:  -46.814020 | E_var:     0.1642 | E_err:   0.006331
[2025-10-07 18:59:08] [Iter  928/1050] R2[477/600]   | LR: 0.007504 | E:  -46.808662 | E_var:     0.1820 | E_err:   0.006666
[2025-10-07 18:59:16] [Iter  929/1050] R2[478/600]   | LR: 0.007465 | E:  -46.817338 | E_var:     0.1572 | E_err:   0.006195
[2025-10-07 18:59:23] [Iter  930/1050] R2[479/600]   | LR: 0.007426 | E:  -46.806976 | E_var:     0.1464 | E_err:   0.005978
[2025-10-07 18:59:31] [Iter  931/1050] R2[480/600]   | LR: 0.007387 | E:  -46.825004 | E_var:     0.2227 | E_err:   0.007374
[2025-10-07 18:59:39] [Iter  932/1050] R2[481/600]   | LR: 0.007349 | E:  -46.807007 | E_var:     0.1757 | E_err:   0.006549
[2025-10-07 18:59:47] [Iter  933/1050] R2[482/600]   | LR: 0.007311 | E:  -46.814801 | E_var:     0.1760 | E_err:   0.006555
[2025-10-07 18:59:54] [Iter  934/1050] R2[483/600]   | LR: 0.007273 | E:  -46.803128 | E_var:     0.2356 | E_err:   0.007584
[2025-10-07 19:00:02] [Iter  935/1050] R2[484/600]   | LR: 0.007236 | E:  -46.818366 | E_var:     0.1545 | E_err:   0.006143
[2025-10-07 19:00:10] [Iter  936/1050] R2[485/600]   | LR: 0.007198 | E:  -46.807639 | E_var:     0.1769 | E_err:   0.006571
[2025-10-07 19:00:18] [Iter  937/1050] R2[486/600]   | LR: 0.007161 | E:  -46.812891 | E_var:     0.1461 | E_err:   0.005973
[2025-10-07 19:00:26] [Iter  938/1050] R2[487/600]   | LR: 0.007125 | E:  -46.803102 | E_var:     0.2596 | E_err:   0.007960
[2025-10-07 19:00:33] [Iter  939/1050] R2[488/600]   | LR: 0.007088 | E:  -46.812125 | E_var:     0.1624 | E_err:   0.006297
[2025-10-07 19:00:41] [Iter  940/1050] R2[489/600]   | LR: 0.007052 | E:  -46.822198 | E_var:     0.1444 | E_err:   0.005937
[2025-10-07 19:00:49] [Iter  941/1050] R2[490/600]   | LR: 0.007017 | E:  -46.811323 | E_var:     0.1352 | E_err:   0.005745
[2025-10-07 19:00:57] [Iter  942/1050] R2[491/600]   | LR: 0.006981 | E:  -46.813764 | E_var:     0.1843 | E_err:   0.006708
[2025-10-07 19:01:05] [Iter  943/1050] R2[492/600]   | LR: 0.006946 | E:  -46.812398 | E_var:     0.1486 | E_err:   0.006023
[2025-10-07 19:01:12] [Iter  944/1050] R2[493/600]   | LR: 0.006911 | E:  -46.808864 | E_var:     0.1945 | E_err:   0.006891
[2025-10-07 19:01:20] [Iter  945/1050] R2[494/600]   | LR: 0.006876 | E:  -46.805208 | E_var:     0.1741 | E_err:   0.006519
[2025-10-07 19:01:28] [Iter  946/1050] R2[495/600]   | LR: 0.006842 | E:  -46.806187 | E_var:     0.1556 | E_err:   0.006164
[2025-10-07 19:01:36] [Iter  947/1050] R2[496/600]   | LR: 0.006808 | E:  -46.812132 | E_var:     0.1469 | E_err:   0.005990
[2025-10-07 19:01:44] [Iter  948/1050] R2[497/600]   | LR: 0.006774 | E:  -46.819158 | E_var:     0.1631 | E_err:   0.006309
[2025-10-07 19:01:51] [Iter  949/1050] R2[498/600]   | LR: 0.006741 | E:  -46.804142 | E_var:     0.1628 | E_err:   0.006305
[2025-10-07 19:01:59] [Iter  950/1050] R2[499/600]   | LR: 0.006708 | E:  -46.819544 | E_var:     0.2320 | E_err:   0.007526
[2025-10-07 19:02:07] [Iter  951/1050] R2[500/600]   | LR: 0.006675 | E:  -46.822656 | E_var:     0.1848 | E_err:   0.006717
[2025-10-07 19:02:15] [Iter  952/1050] R2[501/600]   | LR: 0.006642 | E:  -46.821644 | E_var:     0.1344 | E_err:   0.005728
[2025-10-07 19:02:23] [Iter  953/1050] R2[502/600]   | LR: 0.006610 | E:  -46.804088 | E_var:     0.1678 | E_err:   0.006400
[2025-10-07 19:02:30] [Iter  954/1050] R2[503/600]   | LR: 0.006578 | E:  -46.820200 | E_var:     0.1871 | E_err:   0.006759
[2025-10-07 19:02:38] [Iter  955/1050] R2[504/600]   | LR: 0.006546 | E:  -46.810037 | E_var:     0.1578 | E_err:   0.006206
[2025-10-07 19:02:46] [Iter  956/1050] R2[505/600]   | LR: 0.006515 | E:  -46.805606 | E_var:     0.1619 | E_err:   0.006288
[2025-10-07 19:02:54] [Iter  957/1050] R2[506/600]   | LR: 0.006484 | E:  -46.804450 | E_var:     0.1772 | E_err:   0.006577
[2025-10-07 19:03:01] [Iter  958/1050] R2[507/600]   | LR: 0.006453 | E:  -46.808620 | E_var:     0.1674 | E_err:   0.006392
[2025-10-07 19:03:09] [Iter  959/1050] R2[508/600]   | LR: 0.006422 | E:  -46.817918 | E_var:     0.2039 | E_err:   0.007055
[2025-10-07 19:03:17] [Iter  960/1050] R2[509/600]   | LR: 0.006392 | E:  -46.814092 | E_var:     0.1668 | E_err:   0.006382
[2025-10-07 19:03:25] [Iter  961/1050] R2[510/600]   | LR: 0.006362 | E:  -46.816942 | E_var:     0.1802 | E_err:   0.006633
[2025-10-07 19:03:33] [Iter  962/1050] R2[511/600]   | LR: 0.006333 | E:  -46.813910 | E_var:     0.1911 | E_err:   0.006831
[2025-10-07 19:03:40] [Iter  963/1050] R2[512/600]   | LR: 0.006304 | E:  -46.808581 | E_var:     0.1884 | E_err:   0.006781
[2025-10-07 19:03:48] [Iter  964/1050] R2[513/600]   | LR: 0.006275 | E:  -46.817956 | E_var:     0.1705 | E_err:   0.006452
[2025-10-07 19:03:56] [Iter  965/1050] R2[514/600]   | LR: 0.006246 | E:  -46.805135 | E_var:     0.1725 | E_err:   0.006490
[2025-10-07 19:04:04] [Iter  966/1050] R2[515/600]   | LR: 0.006218 | E:  -46.803637 | E_var:     0.1410 | E_err:   0.005866
[2025-10-07 19:04:12] [Iter  967/1050] R2[516/600]   | LR: 0.006190 | E:  -46.806848 | E_var:     0.2012 | E_err:   0.007008
[2025-10-07 19:04:19] [Iter  968/1050] R2[517/600]   | LR: 0.006162 | E:  -46.813755 | E_var:     0.1453 | E_err:   0.005955
[2025-10-07 19:04:27] [Iter  969/1050] R2[518/600]   | LR: 0.006135 | E:  -46.821369 | E_var:     0.1941 | E_err:   0.006884
[2025-10-07 19:04:35] [Iter  970/1050] R2[519/600]   | LR: 0.006107 | E:  -46.813006 | E_var:     0.1839 | E_err:   0.006701
[2025-10-07 19:04:43] [Iter  971/1050] R2[520/600]   | LR: 0.006081 | E:  -46.804569 | E_var:     0.2153 | E_err:   0.007250
[2025-10-07 19:04:51] [Iter  972/1050] R2[521/600]   | LR: 0.006054 | E:  -46.812069 | E_var:     0.3458 | E_err:   0.009189
[2025-10-07 19:04:58] [Iter  973/1050] R2[522/600]   | LR: 0.006028 | E:  -46.801069 | E_var:     0.1454 | E_err:   0.005958
[2025-10-07 19:05:06] [Iter  974/1050] R2[523/600]   | LR: 0.006002 | E:  -46.817152 | E_var:     0.1727 | E_err:   0.006493
[2025-10-07 19:05:14] [Iter  975/1050] R2[524/600]   | LR: 0.005977 | E:  -46.817052 | E_var:     0.1576 | E_err:   0.006202
[2025-10-07 19:05:22] [Iter  976/1050] R2[525/600]   | LR: 0.005952 | E:  -46.810410 | E_var:     0.1365 | E_err:   0.005772
[2025-10-07 19:05:29] [Iter  977/1050] R2[526/600]   | LR: 0.005927 | E:  -46.808985 | E_var:     0.1413 | E_err:   0.005873
[2025-10-07 19:05:37] [Iter  978/1050] R2[527/600]   | LR: 0.005902 | E:  -46.805916 | E_var:     0.1739 | E_err:   0.006516
[2025-10-07 19:05:45] [Iter  979/1050] R2[528/600]   | LR: 0.005878 | E:  -46.815278 | E_var:     0.1358 | E_err:   0.005757
[2025-10-07 19:05:53] [Iter  980/1050] R2[529/600]   | LR: 0.005854 | E:  -46.814812 | E_var:     0.1793 | E_err:   0.006616
[2025-10-07 19:06:01] [Iter  981/1050] R2[530/600]   | LR: 0.005830 | E:  -46.816638 | E_var:     0.2150 | E_err:   0.007245
[2025-10-07 19:06:08] [Iter  982/1050] R2[531/600]   | LR: 0.005807 | E:  -46.807184 | E_var:     0.1999 | E_err:   0.006985
[2025-10-07 19:06:16] [Iter  983/1050] R2[532/600]   | LR: 0.005784 | E:  -46.810922 | E_var:     0.2639 | E_err:   0.008027
[2025-10-07 19:06:24] [Iter  984/1050] R2[533/600]   | LR: 0.005761 | E:  -46.807377 | E_var:     0.1858 | E_err:   0.006734
[2025-10-07 19:06:32] [Iter  985/1050] R2[534/600]   | LR: 0.005739 | E:  -46.810112 | E_var:     0.1640 | E_err:   0.006328
[2025-10-07 19:06:40] [Iter  986/1050] R2[535/600]   | LR: 0.005717 | E:  -46.808655 | E_var:     0.1477 | E_err:   0.006004
[2025-10-07 19:06:47] [Iter  987/1050] R2[536/600]   | LR: 0.005695 | E:  -46.807398 | E_var:     0.1644 | E_err:   0.006335
[2025-10-07 19:06:55] [Iter  988/1050] R2[537/600]   | LR: 0.005674 | E:  -46.808425 | E_var:     0.1900 | E_err:   0.006811
[2025-10-07 19:07:03] [Iter  989/1050] R2[538/600]   | LR: 0.005653 | E:  -46.819827 | E_var:     0.2391 | E_err:   0.007640
[2025-10-07 19:07:11] [Iter  990/1050] R2[539/600]   | LR: 0.005632 | E:  -46.815259 | E_var:     0.1721 | E_err:   0.006482
[2025-10-07 19:07:18] [Iter  991/1050] R2[540/600]   | LR: 0.005612 | E:  -46.820095 | E_var:     0.1944 | E_err:   0.006889
[2025-10-07 19:07:26] [Iter  992/1050] R2[541/600]   | LR: 0.005592 | E:  -46.814136 | E_var:     0.1610 | E_err:   0.006269
[2025-10-07 19:07:34] [Iter  993/1050] R2[542/600]   | LR: 0.005572 | E:  -46.804749 | E_var:     0.1546 | E_err:   0.006144
[2025-10-07 19:07:42] [Iter  994/1050] R2[543/600]   | LR: 0.005553 | E:  -46.813204 | E_var:     0.2105 | E_err:   0.007169
[2025-10-07 19:07:50] [Iter  995/1050] R2[544/600]   | LR: 0.005534 | E:  -46.810085 | E_var:     0.1594 | E_err:   0.006238
[2025-10-07 19:07:57] [Iter  996/1050] R2[545/600]   | LR: 0.005515 | E:  -46.807094 | E_var:     0.2347 | E_err:   0.007569
[2025-10-07 19:08:05] [Iter  997/1050] R2[546/600]   | LR: 0.005496 | E:  -46.818505 | E_var:     0.1606 | E_err:   0.006262
[2025-10-07 19:08:13] [Iter  998/1050] R2[547/600]   | LR: 0.005478 | E:  -46.810770 | E_var:     0.1844 | E_err:   0.006710
[2025-10-07 19:08:21] [Iter  999/1050] R2[548/600]   | LR: 0.005460 | E:  -46.816658 | E_var:     0.1673 | E_err:   0.006390
[2025-10-07 19:08:29] [Iter 1000/1050] R2[549/600]   | LR: 0.005443 | E:  -46.806315 | E_var:     0.1944 | E_err:   0.006889
[2025-10-07 19:08:29] ✓ Checkpoint saved: checkpoint_iter_001000.pkl
[2025-10-07 19:08:37] [Iter 1001/1050] R2[550/600]   | LR: 0.005426 | E:  -46.813875 | E_var:     0.2257 | E_err:   0.007424
[2025-10-07 19:08:44] [Iter 1002/1050] R2[551/600]   | LR: 0.005409 | E:  -46.800858 | E_var:     0.1973 | E_err:   0.006940
[2025-10-07 19:08:52] [Iter 1003/1050] R2[552/600]   | LR: 0.005393 | E:  -46.808905 | E_var:     0.2032 | E_err:   0.007043
[2025-10-07 19:09:00] [Iter 1004/1050] R2[553/600]   | LR: 0.005377 | E:  -46.812883 | E_var:     0.3358 | E_err:   0.009054
[2025-10-07 19:09:08] [Iter 1005/1050] R2[554/600]   | LR: 0.005361 | E:  -46.812193 | E_var:     0.2039 | E_err:   0.007055
[2025-10-07 19:09:16] [Iter 1006/1050] R2[555/600]   | LR: 0.005345 | E:  -46.808408 | E_var:     0.1721 | E_err:   0.006483
[2025-10-07 19:09:23] [Iter 1007/1050] R2[556/600]   | LR: 0.005330 | E:  -46.804366 | E_var:     0.3107 | E_err:   0.008710
[2025-10-07 19:09:31] [Iter 1008/1050] R2[557/600]   | LR: 0.005315 | E:  -46.811411 | E_var:     0.1472 | E_err:   0.005994
[2025-10-07 19:09:39] [Iter 1009/1050] R2[558/600]   | LR: 0.005301 | E:  -46.816389 | E_var:     0.1440 | E_err:   0.005928
[2025-10-07 19:09:47] [Iter 1010/1050] R2[559/600]   | LR: 0.005287 | E:  -46.813766 | E_var:     0.2127 | E_err:   0.007206
[2025-10-07 19:09:55] [Iter 1011/1050] R2[560/600]   | LR: 0.005273 | E:  -46.812360 | E_var:     0.1623 | E_err:   0.006295
[2025-10-07 19:10:02] [Iter 1012/1050] R2[561/600]   | LR: 0.005260 | E:  -46.811553 | E_var:     0.2659 | E_err:   0.008057
[2025-10-07 19:10:10] [Iter 1013/1050] R2[562/600]   | LR: 0.005247 | E:  -46.818279 | E_var:     0.1538 | E_err:   0.006128
[2025-10-07 19:10:18] [Iter 1014/1050] R2[563/600]   | LR: 0.005234 | E:  -46.809028 | E_var:     0.1647 | E_err:   0.006342
[2025-10-07 19:10:26] [Iter 1015/1050] R2[564/600]   | LR: 0.005221 | E:  -46.807701 | E_var:     0.2009 | E_err:   0.007003
[2025-10-07 19:10:34] [Iter 1016/1050] R2[565/600]   | LR: 0.005209 | E:  -46.813170 | E_var:     0.1783 | E_err:   0.006597
[2025-10-07 19:10:41] [Iter 1017/1050] R2[566/600]   | LR: 0.005198 | E:  -46.820272 | E_var:     0.1752 | E_err:   0.006540
[2025-10-07 19:10:49] [Iter 1018/1050] R2[567/600]   | LR: 0.005186 | E:  -46.811510 | E_var:     0.1524 | E_err:   0.006099
[2025-10-07 19:10:57] [Iter 1019/1050] R2[568/600]   | LR: 0.005175 | E:  -46.817589 | E_var:     0.1512 | E_err:   0.006076
[2025-10-07 19:11:05] [Iter 1020/1050] R2[569/600]   | LR: 0.005164 | E:  -46.812519 | E_var:     0.1691 | E_err:   0.006426
[2025-10-07 19:11:12] [Iter 1021/1050] R2[570/600]   | LR: 0.005154 | E:  -46.799255 | E_var:     0.1963 | E_err:   0.006922
[2025-10-07 19:11:20] [Iter 1022/1050] R2[571/600]   | LR: 0.005144 | E:  -46.817497 | E_var:     0.1606 | E_err:   0.006262
[2025-10-07 19:11:28] [Iter 1023/1050] R2[572/600]   | LR: 0.005134 | E:  -46.814223 | E_var:     0.2102 | E_err:   0.007164
[2025-10-07 19:11:36] [Iter 1024/1050] R2[573/600]   | LR: 0.005125 | E:  -46.806573 | E_var:     0.1824 | E_err:   0.006673
[2025-10-07 19:11:44] [Iter 1025/1050] R2[574/600]   | LR: 0.005116 | E:  -46.810430 | E_var:     0.1490 | E_err:   0.006032
[2025-10-07 19:11:51] [Iter 1026/1050] R2[575/600]   | LR: 0.005107 | E:  -46.821950 | E_var:     0.1971 | E_err:   0.006936
[2025-10-07 19:11:59] [Iter 1027/1050] R2[576/600]   | LR: 0.005099 | E:  -46.794588 | E_var:     0.1774 | E_err:   0.006582
[2025-10-07 19:12:07] [Iter 1028/1050] R2[577/600]   | LR: 0.005091 | E:  -46.814832 | E_var:     0.1519 | E_err:   0.006091
[2025-10-07 19:12:15] [Iter 1029/1050] R2[578/600]   | LR: 0.005083 | E:  -46.822209 | E_var:     0.2045 | E_err:   0.007066
[2025-10-07 19:12:23] [Iter 1030/1050] R2[579/600]   | LR: 0.005075 | E:  -46.816269 | E_var:     0.1842 | E_err:   0.006705
[2025-10-07 19:12:30] [Iter 1031/1050] R2[580/600]   | LR: 0.005068 | E:  -46.815660 | E_var:     0.1701 | E_err:   0.006444
[2025-10-07 19:12:38] [Iter 1032/1050] R2[581/600]   | LR: 0.005062 | E:  -46.819975 | E_var:     0.1825 | E_err:   0.006675
[2025-10-07 19:12:46] [Iter 1033/1050] R2[582/600]   | LR: 0.005055 | E:  -46.804156 | E_var:     0.1884 | E_err:   0.006782
[2025-10-07 19:12:54] [Iter 1034/1050] R2[583/600]   | LR: 0.005049 | E:  -46.807611 | E_var:     0.1613 | E_err:   0.006275
[2025-10-07 19:13:02] [Iter 1035/1050] R2[584/600]   | LR: 0.005044 | E:  -46.808968 | E_var:     0.1511 | E_err:   0.006074
[2025-10-07 19:13:09] [Iter 1036/1050] R2[585/600]   | LR: 0.005039 | E:  -46.815218 | E_var:     0.1574 | E_err:   0.006199
[2025-10-07 19:13:17] [Iter 1037/1050] R2[586/600]   | LR: 0.005034 | E:  -46.808149 | E_var:     0.1894 | E_err:   0.006801
[2025-10-07 19:13:25] [Iter 1038/1050] R2[587/600]   | LR: 0.005029 | E:  -46.803498 | E_var:     0.1906 | E_err:   0.006821
[2025-10-07 19:13:33] [Iter 1039/1050] R2[588/600]   | LR: 0.005025 | E:  -46.812433 | E_var:     0.1745 | E_err:   0.006527
[2025-10-07 19:13:40] [Iter 1040/1050] R2[589/600]   | LR: 0.005021 | E:  -46.810174 | E_var:     0.1242 | E_err:   0.005506
[2025-10-07 19:13:48] [Iter 1041/1050] R2[590/600]   | LR: 0.005017 | E:  -46.813839 | E_var:     0.1940 | E_err:   0.006881
[2025-10-07 19:13:56] [Iter 1042/1050] R2[591/600]   | LR: 0.005014 | E:  -46.814219 | E_var:     0.1509 | E_err:   0.006070
[2025-10-07 19:14:04] [Iter 1043/1050] R2[592/600]   | LR: 0.005011 | E:  -46.817629 | E_var:     0.2360 | E_err:   0.007591
[2025-10-07 19:14:12] [Iter 1044/1050] R2[593/600]   | LR: 0.005008 | E:  -46.810262 | E_var:     0.1828 | E_err:   0.006680
